import json

# 读取原始文件
class_tech_catalog_path = '/home/<USER>/dev/tenyy-dind/tenyy/src/apkinfo_analysis/apk_analysis_flow/assets/class_tech_catalog.json'
with open(class_tech_catalog_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
    # 获取实际的技术分类数据
    class_tech_catalog = data["technologies"]

# 创建新的数据结构
new_catalog = {"technologies": {}}

# 遍历原始数据
for category, subcategories in class_tech_catalog.items():
    if category not in new_catalog["technologies"]:
        new_catalog["technologies"][category] = {}
    for subcategory, sdks in subcategories.items():
        new_catalog["technologies"][category][subcategory] = []
        # 遍历每个SDK，创建独立的条目
        for sdk_name, package_name in sdks.items():
            # 创建新的SDK条目，包含所有需要的字段
            sdk_entry = {
                "sdkName": sdk_name,
                "typeName": subcategory,
                "companyName": "",
                "version": "",
                "officialWeb": "",
                "sdkDocumentUrl": "",
                "privacyPolicyUrl": "",
                "sdkComplianceInstructionsUrl": "",
                "packageName": package_name,
                "enhancedDescription": "",
                "briefMsg": ""
            }
            new_catalog["technologies"][category][subcategory].append(sdk_entry)

# 将新数据写入文件
output_path = '/home/<USER>/dev/tenyy-dind/tenyy/src/apkinfo_analysis/apk_analysis_flow/assets/reorganized_class_tech_catalog.json'
with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(new_catalog, f, ensure_ascii=False, indent=2)

print(f"Reorganized catalog saved to {output_path}")