{"technologies": {"Advertising and Marketing": {"Advertising Aggregation": {"ADmobile": "dontknow", "AdMob Mediation Adapter (AdColony)": "com.google.ads.mediation.adcolony", "AdMob Mediation Adapter (Applovin)": "com.google.ads.mediation.applovin", "AdMob Mediation Adapter (Chartboost)": "com.google.ads.mediation.chartboost", "AdMob Mediation Adapter (Facebook)": "com.google.ads.mediation.facebook", "AdMob Mediation Adapter (Flurry)": "com.google.ads.mediation.flurry", "AdMob Mediation Adapter (Fyber)": "com.google.ads.mediation.fyber", "AdMob Mediation Adapter (InMobi)": "com.google.ads.mediation.inmobi", "AdMob Mediation Adapter (MoPub)": "dontknow", "AdMob Mediation Adapter (Tapjoy)": "com.google.ads.mediation.tapjoy", "AdMob Mediation Adapter (Unity)": "com.google.ads.mediation.unity", "AdMob Mediation Adapter (Vungle)": "com.google.ads.mediation.vungle", "AdMob Mediation Adapter (ironSource)": "com.google.ads.mediation.ironsource", "AdMob Mediation Adapters": "dontknow", "Adlefee": "dontknow", "Adlib": "dontknow", "Ampiri": "dontknow", "AppLovin Mediation Adapters": "dontknow", "Appnexus Mediation Adapter (AdMob)": "dontknow", "Appnexus Mediation Adapter (Amazon)": "dontknow", "Appnexus Mediation Adapter (Facebook)": "dontknow", "Appnexus Mediation Adapter (Millenialmedia)": "dontknow", "Appnexus Mediation Adapter (MoPub)": "dontknow", "Appnexus Mediation Adapter (SmartAdServer)": "dontknow", "Appodeal": "com.appodeal.sdk", "BeiZi": "dontknow", "Google AdWhirl": "com.google.ads.AdWhirl", "Goprimer": "dontknow", "HyprMX": "com.hyprmx.sdk.android", "Instal": "dontknow", "Ironsource Mediation Adapter (AdColony)": "com.ironsource.adapters.adcolony", "Ironsource Mediation Adapter (AdMob)": "com.ironsource.adapters.admob", "Ironsource Mediation Adapter (AppLovin)": "com.ironsource.adapters.applovin", "Ironsource Mediation Adapter (Chartboost)": "com.ironsource.adapters.chartboost", "Ironsource Mediation Adapter (Facebook)": "com.ironsource.adapters.facebook", "Ironsource Mediation Adapter (HyprMX)": "com.ironsource.adapters.hyprmx", "Ironsource Mediation Adapter (InMobi)": "com.ironsource.adapters.inmobi", "Ironsource Mediation Adapter (Tapjoy)": "com.ironsource.adapters.tapjoy", "Ironsource Mediaton Adapter (UnityAds)": "com.ironsource.adapters.unityads", "Ironsource Mediaton Adapter (Vungle)": "com.ironsource.adapters.vungle", "Jpush SSP": "dontknow", "Kaijia Ads": "dontknow", "Kakao AdFit": "com.kakao.adfit.ads", "Mangolm Ads": "dontknow", "MoPub Mediation Adapter (AdColony)": "com.mopub.mobileads.AdColony", "MoPub Mediation Adapter (AdMob)": "com.mopub.mobileads.GooglePlayServices", "MoPub Mediation Adapter (AppLovin)": "com.mopub.mobileads.AppLovin", "MoPub Mediation Adapter (Chartboost)": "com.mopub.mobileads.Chartboost", "MoPub Mediation Adapter (Facebook)": "com.mopub.mobileads.FacebookAudienceNetwork", "MoPub Mediation Adapter (Fyber)": "dontknow", "MoPub Mediation Adapter (InMobi)": "com.mopub.mobileads.InMobi", "MoPub Mediation Adapter (IronSource)": "com.mopub.mobileads.IronSource", "MoPub Mediation Adapter (Mintegral)": "com.mopub.mobileads.Mintegral", "MoPub Mediation Adapter (Ogury)": "dontknow", "MoPub Mediation Adapter (Pangle)": "com.mopub.mobileads.Pangle", "MoPub Mediation Adapter (Snap)": "com.mopub.mobileads.Snap", "MoPub Mediation Adapter (Tapjoy)": "com.mopub.mobileads.Tapjoy", "MoPub Mediation Adapter (Unity)": "com.mopub.mobileads.Unity", "MoPub Mediation Adapter (Verizon Media)": "com.mopub.mobileads.Verizon", "MoPub Mediation Adapter (Vungle)": "com.mopub.mobileads.Vungle", "MoPub Mediation Adapters": "dontknow", "Quicksdk GLink": "dontknow", "Salesforce Audience Studio": "com.salesforce.android.krux", "Tapdaq Mediation Adapter (Admob)": "com.tapdaq.adapters.admob", "Tapdaq Mediation Adapter (Applovin)": "com.tapdaq.adapters.applovin", "Tapdaq Mediation Adapter (Chartboost)": "com.tapdaq.adapters.chartboost", "Tapdaq Mediation Adapter (Facebook)": "com.tapdaq.adapters.facebook", "Tapdaq Mediation Adapter (IronSource)": "com.tapdaq.adapters.ironsource", "Tapdaq Mediation Adapter (Tapjoy)": "com.tapdaq.adapters.tapjoy", "Tapdaq Mediation Adapter (Unity)": "com.tapdaq.adapters.unity", "Tapdaq Mediation Adapter (Vungle)": "com.tapdaq.adapters.vungle", "ToBid Advertising Aggregation": "dontknow", "TopOn": "com.tradplus.sdk.topon", "TradPlus": "com.tradplus.sdk", "Ubix Advertising": "dontknow", "Uni-Ad Advertising Alliance": "dontknow", "Vungle Mediation Adapter (AdMob)": "dontknow"}, "Advertising Attribution": {"Adform Tracking": "com.adform.tracking", "Airbridge": "io.airbridge.airbridge_flutter_sdk", "AppLovin Adjust": "com.adjust.sdk", "Applivery": "com.applivery.applivery_sdk", "AppsFlyer": "com.appsflyer.android.sdk", "BurtTracker": "dontknow", "Comscore": "com.comscore", "Criteo Publisher": "com.criteo.publisher", "IAB OM Open Measurement": "com.iab.omid.library.vungle", "IGAWorks AdBrix": "com.igaworks.adbrix", "Mobiquity Networks": "dontknow", "Purchasely": "com.purchasely.purchasely-android", "QuickAD+": "dontknow", "Reveal Mobile": "dontknow", "Reyun TrackingIO": "com.reyun.trackingio", "Singular": "com.singular.sdk", "Soomla Ad Quality": "com.soomla.adquality", "TalkingData Ad Tracking": "com.tendcloud.appcpa", "Tappx": "com.tappx.sdk.android", "Tapstream": "com.tapstream.sdk", "Tenjin": "com.tenjin.android", "Triton Digital Mobile": "com.tritondigital.mobile.sdk", "Yandex AppMetrica": "com.yandex.metrica"}, "Advertising and Monetization": {"Applovin MAX": "com.applovin.sdk", "233 LeYuan Advertising": "dontknow", "AMoAd": "com.amoad", "AdAdapted": "com.adadapted.sdk", "AdBuddiz": "com.adincube.sdk", "AdCel": "com.adcel.sdk", "AdFalcon": "com.adfalcon.sdk", "AdGeneration": "com.adgeneration", "AdJoe": "io.adjoe.sdk", "AdStir": "com.adstir.sdk", "AdView": "com.adview.sdk", "Adcash": "com.adcash.sdk", "Adison Offerwall": "com.adison.offerwall", "Admixer": "com.admixer.sdk", "Admost Mediation Router": "com.admost.sdk", "Adtapsy": "com.adtapsy.sdk", "AerServ": "com.aerserv.sdk", "Airnow Media": "com.airnow.media", "Airship Accengage": "com.accengage.sdk", "Alibabagroup Ads": "dontknow", "AppBrain": "com.appbrain.sdk", "AppMonet": "com.appmonet.sdk", "AppNexus": "com.appnexus.opensdk", "Appmediation": "com.appmediation.sdk", "Appnext": "com.appnext", "Appsfire": "dontknow", "AyeT Studios Publisher": "com.ayetstudios.publishersdk", "Baidu Alliance (Baiqingtou)": "dontknow", "Baidu oCPC": "dontknow", "BidMachine": "io.bidmachine", "Bigo Ads": "sg.bigo.ads", "BlueKai by Oracle Data Cloud": "dontknow", "Buzzvil": "com.buzzvil.buzzscreen", "Byte Jumping Through The Door": "dontknow", "Cauly": "com.cauly.android.ad", "Chartboost": "com.chartboost.sdk", "Cheetah Mobile Ads": "com.cmcm.adsdk", "Cloudmobi": "dontknow", "ConsoliAds": "com.consoliads.sdk", "Conversant Advertiser": "dontknow", "Digital Turbine AdColony": "com.adcolony.sdk", "Digital Turbine AdMarvel": "com.admarvel.android.ads", "Display.Io": "io.display.sdk", "Domob Ads": "cn.domob.android.ads", "Facebook Audience Network": "com.facebook.ads", "Fiksu": "com.fiksu.sdk", "Fireeye Ads": "dontknow", "Flurry": "com.flurry.android", "Flymob": "com.flymob.sdk", "Freestar": "com.freestar.android.sdk", "Freewheel AdManager": "tv.freewheel.ad.manager", "Fyber Fairbid": "com.fyber.fairbid", "Fyber Marketplace - InnerActive": "com.fyber.inneractive.sdk", "Fyber Offer Wall Edge": "com.fyber.offerwalle.sdk", "Glispa Connect": "dontknow", "Google AdMob": "com.google.android.gms.ads", "Google Interactive Media Ads (IMA)": "com.google.ads.interactivemedia.v3", "Greystripe": "dontknow", "Hongtussp Ads": "dontknow", "Huawei Advertising Services": "com.huawei.hms.ads", "HyperADX": "com.hyperadx.sdk", "I-mobile": "jp.ne.imobile.sdk", "IGAWorks adPOPcorn": "com.igaworks.adpopcorn", "Iflytek Advertising": "dontknow", "InBrain Surveys": "com.inbrain.sdk", "InMobi": "com.inmobi.sdk", "Insider": "com.useinsider.insider", "Instreamatic": "com.instreamatic.sdk", "Intowow": "com.intowow.sdk", "Ironsource": "com.ironsource.sdk", "Jingzhuntong Advertising": "dontknow", "Jpush Alliance Junion": "dontknow", "KIDOZ": "com.kidoz.sdk.api", "Kakao Ad": "com.kakao.ad", "Kwai Affiliate": "com.kwai.anticheat", "Line Ads": "com.linecorp.ads", "MMA Advertising Monitoring": "dontknow", "MSMobAdSDK adxdata AdMate": "dontknow", "Madvertise MngAds": "de.madvertise.android.sdk", "Maio": "jp.maio.sdk.android", "MdotM Ads": "dontknow", "Media.net Ads": "com.media.net.sdk", "Mi Mobile Advertising Alliance": "dontknow", "Millennial Media": "com.millennialmedia.android", "Mintegral": "com.mintegral.msdk", "Mintegral Ad (Banner)": "com.mintegral.msdk.out.banner", "Mintegral Ad (Bidding)": "dontknow", "Mintegral Ad (InterActive)": "dontknow", "Mintegral Ad (Interstitial)": "com.mintegral.msdk.out.interstitial", "Mintegral Ad (Reward)": "com.mintegral.msdk.out.reward", "Mintegral Ad (Splash)": "com.mintegral.msdk.out.splash", "Mnectar": "dontknow", "MoPub": "com.mopub.mobileads", "MobWIN ads": "com.mobwin.sdk", "Mobfox": "com.mobfox.sdk", "Moloco Ad Cloud": "com.moloco.sdk", "MyTarget": "com.my.target.sdk", "Nami": "com.namiml.Nami", "Nativo": "net.nativo.sdk", "Nend": "net.nend.android", "Nexage SourceKit-MRAID": "dontknow", "NinthDecimal Kiip": "me.kiip.sdk", "Ogury": "io.ogury.sdk", "OneWay Advertising": "dontknow", "Oppo Advertising Alliance": "dontknow", "Outbrain": "com.outbrain.sdk", "Persona.ly": "com.persona.sdk", "Pokkt": "com.pokkt.sdk", "Prebid Mobile": "org.prebid.mobile", "PubMatic OpenWrap": "com.pubmatic.sdk.openwrap", "RevMob": "com.revmob", "Sigmob Mobile Advertising Platform": "com.sigmob.sdk", "Smaato NextGen": "com.smaato.soma", "Smart AdServer": "com.smartadserver.android.sdk", "SpotX": "com.spotxchange.sdk.android", "SuperAwesome Ads": "tv.superawesome.sdk.publisher", "TNK Factory": "com.tnkfactory.ad", "Taboola": "com.taboola.android.sdk", "Tapdaq Base": "com.tapdaq.sdk", "Tapjoy": "com.tapjoy", "Tapsell": "ir.tapsell.sdk", "Teads": "com.teads.sdk.android", "Tencent Ads": "com.qq.e.ads", "Tencent Youlianghui": "com.qq.e.union", "TheoremReach": "com.theoremreach.theoremreach", "Tianmu Advertising": "dontknow", "TradPlus": "com.tradplus.sdk", "Tremor Video": "com.tremorvideo.sdk.android.videoad", "Tuibear Ads": "dontknow", "U8SDK-Advertising": "dontknow", "Unity Ads": "com.unity3d.ads", "Verizon Ads": "com.verizon.ads", "Verve Group HyBid": "net.pubnative.lite.sdk", "Vpon ad": "com.vpon.sdk", "Vungle": "com.vungle.warren", "Widespace": "com.widespace.sdk", "Winnershang Ads": "dontknow", "Wqmobile Ads": "dontknow", "Xiaoman Ads": "dontknow", "Yandex Mobile Ads": "com.yandex.mobile.ads", "Yieldmo": "com.yieldmo.android", "Yomob Advertising": "dontknow", "YouAppi": "com.youappi.sdk", "Youdao Ads": "com.youdao.ad", "Youdao Zhixuan": "dontknow", "Youmi": "net.youmi.android", "Youmi Ads": "net.youmi.android.offers", "Youmi Integral Wall": "dontknow", "Znds Ads": "dontknow", "Zucks Ad Network": "jp.co.zucks.android.sdk"}, "Content Marketing": {"AdTiming OpenMediation": "com.adtiming.openmediation", "Armchair": "dontknow", "Bazaarvoice": "com.bazaarvoice.bvandroidsdk", "Kuaizhan Yunping": "dontknow"}, "Marketing Platform": {"Adobe Experience Platform": "com.adobe.marketing.mobile", "AppManago SalesManago": "com.appmanago.salesmanago", "Appoxee": "dontknow", "Button Marketplace": "com.usebutton.sdk.button", "Conviva": "com.conviva.sdk", "Emma": "com.emma.android.sdk", "Eulerian Analytics": "com.eulerian.android.sdk", "MParticle": "com.mparticle.android.sdk", "Marketo": "com.marketo.android.sdk", "Pulsate": "com.pulsate.android.sdk", "Salesforce Marketing Cloud": "com.salesforce.marketingcloud.messages", "Salesforce Service": "com.salesforce.android.service.chat", "Swrve": "com.swrve.sdk.android", "Upsight": "com.upsight.android", "Volcengine Intelligent Marketing": "dontknow"}, "Marketing Tools": {"Airship Preference Center": "com.urbanairship.preferencecenter", "Appvirality": "com.appvirality.android", "Clover": "com.clover.sdk.v3.remotepay", "Doorbell": "io.doorbell.android", "Mailchimp": "com.mailchimp", "Quantumgraph": "dontknow", "Talkable": "com.talkable.sdk", "VI Search": "dontknow"}}, "Analytics": {"A/B Testing": {"AppAdhoc A/B Testing": "com.appadhoc.sdk.android", "Apptimize": "com.apptimize", "Cloud Eye A/B Testing": "dontknow", "Firebase ABTesting": "com.google.firebase.testing", "Leanplum": "com.leanplum.leanplum-core", "Optimizely": "com.optimizely.ab.android.sdk", "Sensorsdata A/B Testing": "dontknow", "Taplytics": "com.taplytics.sdk", "Tencent Cloud AB Experimental Platform": "dontknow", "TestFairy": "com.testfairy.testfairy-android-sdk", "Vessel": "io.vessel.android"}, "Intelligent Video Analysis": {"AWS Rekognition": "com.amazonaws.services.rekognition", "Adobe Experience Platform - Media Analytics": "com.adobe.marketing.mobile.media", "Mux": "com.mux.stats.sdk.muxstats"}, "User Behavior Analysis": {"AT Internet": "com.atinternet.atinternet-android-sdk", "AWS Kinesis": "com.amazonaws.services.kinesis", "Acoustic Tealeaf": "com.tl.tealeaf", "Adobe Experience Platform - Analytics": "com.adobe.marketing.mobile.analytics", "Airship Analytics": "com.urbanairship.analytics", "Akamai BMP": "dontknow", "Aliyun Mobile Data Analytics": "com.aliyun.ams", "Amplitude Analyze": "com.amplitude.android.sdk", "App Analytics": "dontknow", "Baidu Application Analytics": "dontknow", "Baidu Plugin Analysis": "dontknow", "Baidu Statistics": "com.baidu.mobstat", "Chartbeat": "com.chartbeat.android.sdk", "CleverTap": "com.clevertap.android.sdk", "ContentSquare": "com.contentsquare.android.sdk", "Convertlab": "com.convertlab.android.sdk", "Countly": "ly.count.android.sdk", "Devtodev": "com.devtodev.sdk", "Dynamic Yield": "com.dynamicyield.android", "Emarsys": "com.emarsys.android.sdk", "Emarsys Predict": "com.emarsys.predict", "Embrace Analyze": "io.embrace.android.sdk", "Firebase Analytics": "com.google.firebase.analytics", "Geshu": "dontknow", "Google Analytics": "com.google.android.gms.analytics", "GrowingIO": "com.growingio.android.sdk", "Heap": "com.heapanalytics.android", "Huawei Analytics": "com.huawei.hms.analytics", "Hypers Analytics": "dontknow", "Jpush JAnalytics": "cn.jiguang.analytics.android.api", "KISSmetrics": "com.kissmetrics.sdk", "Karte Core": "io.karte.android", "Karte Visual Tracking": "dontknow", "Kochava": "com.kochava.base", "MatomoTracker": "org.matomo.sdk", "Mixpanel": "com.mixpanel.android.mpmetrics", "Mob MobLink": "com.mob.moblink", "Mytracker": "com.my.tracker.mytracker-sdk", "Nineton Statistical Analysis": "dontknow", "OpenInstall App Channel Statistics": "com.openinstall.sdk", "Parsely": "com.parsely.android.sdk", "PollFish": "com.pollfish.android", "Pyze Analyze": "com.pyze.android.sdk", "Qualtrics Analyze": "com.qualtrics.digital", "Repro Analyze": "io.repro.android.sdk", "Salesforce Mobile": "com.salesforce.androidsdk", "Segment Analytics": "com.segment.analytics.android", "Sensorsdata Analytics": "com.sensorsdata.analytics.android.sdk", "Smartlook": "com.smartlook.sdk.smartlook", "Snowplow Tracker": "com.snowplowanalytics.snowplow.tracker", "Sogou Mobile Application Statistics": "dontknow", "TalkingData App Analytics": "com.tendcloud.tenddata", "Tealium Analyze": "com.tealium.library", "Tencent Lighthouse": "dontknow", "Tencent Mobile Analytics": "com.tencent.stat", "Umeng Mobile Statistics": "com.umeng.analytics", "Upland Localytics": "com.localytics.android", "UserX": "com.userx.sdk", "Verint Foresee": "com.foresee.sdk", "Volcengine Growth Analytics": "dontknow", "Webtrends Mobile": "com.webtrends.mobile.sdk", "Wootric": "com.wootric.androidsdk", "Xtremepush": "com.xtremepush.sdk", "Yuntao": "dontknow", "Zhugeio Io": "com.zhuge.analysis.stat"}, "User Profile": {"BlueConic": "com.blueconic.android.sdk", "Gexiang": "dontknow"}}, "Audio and Video": {"Audio Processing": {"365speak": "dontknow", "AWS Polly": "com.amazonaws.services.polly", "AWS Transcribe": "com.amazonaws.services.transcribe", "Aispeech Full Link Dialogue": "dontknow", "Aispeech Long Speech Recognition": "dontknow", "Aispeech Multi-device Selection": "dontknow", "Aispeech Real-time Long Speech Recognition": "dontknow", "Aispeech Single Sentence Recognition": "dontknow", "Aispeech Speech Recognition": "dontknow", "Aispeech Speech Synthesis": "dontknow", "Aispeech Voice Wakeup": "dontknow", "Aliyun Cloud Intelligent Voice": "dontknow", "Azure Speech Services": "com.microsoft.cognitiveservices.speech", "Baidu Speech Recognition": "com.baidu.speech.asr", "Baidu Speech Synthesis": "com.baidu.speech.tts", "Chivox Intelligent Voice Review": "dontknow", "Chumenwenwen Voice Technology": "dontknow", "EZAudio": "dontknow", "Google Speech Recognition": "dontknow", "Hifiveai Universal": "dontknow", "HwAudioKit": "dontknow", "Iflytek Speech Synthesis": "com.iflytek.speech", "Iflytek Voice Review": "dontknow", "Iflytek Xunfei Voice Recognition": "com.iflytek.cloud", "Iflytek Xunfei Voice Recognition Face": "dontknow", "Iflytek Xunfei Voice Wake Up": "dontknow", "Jd Speech Recognition": "dontknow", "Jd Speech Synthesis": "dontknow", "Kugou Song Library Component": "dontknow", "Microsoft Speech Recognition": "dontknow", "Mp3agic": "com.mpatric.mp3agic", "Niutrans Translation": "dontknow", "ObjectAL": "dontknow", "Sinovoice Speech Recognition ASR": "dontknow", "Sinovoice Speech Synthesis TTS": "dontknow", "Sinovoice Voice Recognition VPR": "dontknow", "Sogou Voice Recognition": "dontknow", "SoundHound Houndify": "com.soundhound.houndify", "Tencent Game Multimedia Engine": "com.tencent.gcloud.voice", "Tencent Voice Recognition": "com.tencent.cloud.ai.speech", "Unisound Speech Synthesis": "dontknow", "Unisound Voice Input": "dontknow", "Volcengine Speech Synthesis": "dontknow", "Yandex SpeechKit": "ru.yandex.speechkit", "Youdao Speech Synthesis": "dontknow", "Youdao Voice Recognition": "dontknow", "Youdao Voice Review": "dontknow", "Zhishiv Laya": "dontknow"}, "Beauty Filter": {"Banuba Face AR": "com.banuba.sdk.facear", "Banuba Video Editor": "com.banuba.sdk.ve", "Camera360": "vStudio.Android.Camera360", "Faceunity Beauty": "com.faceunity.nama", "Immomo Beauty": "dontknow", "Kiwiar Beauty": "dontknow", "Meishesdk Beauty": "dontknow", "Sensetime Beauty": "com.sensetime.stmobile", "StreamLake-MagicEngine": "dontknow", "Tencent Beauty Effects": "com.tencent.liteav.beauty", "Tillusory Beauty": "dontknow", "Tusdk Beauty": "org.lasque.tusdk.core", "Tusdk Image Processing": "dontknow", "Tusdk Special Effects Camera": "dontknow", "Tusdk Video Editing": "dontknow", "Tusdk Video Fusion": "dontknow", "Volcengine Beauty Filters": "dontknow", "Volcengine CV": "dontknow"}, "Interactive Whiteboard": {"Agora Fastboard": "com.github.netless-io.fastboard-android", "AnyRTC Interactive Whiteboard": "org.anyrtc.whiteboard", "Cloudhub Interactive Whiteboard": "dontknow", "Netease Cloud Interactive Whiteboard": "com.netease.yunxin.whiteboard", "Netless Interactive Whiteboard": "com.herewhite.sdk", "Pano Cloud Whiteboard": "com.pano.whiteboard", "Rongcloud Interactive Whiteboard": "cn.rongcloud.whiteboard", "Tencent Interactive Whiteboard": "com.tencent.tic", "Zego Interactive Whiteboard": "im.zego.zegoexpresswb", "Zego Express-Enable-Whiteboard": "im.zego.zegoexpresswb"}, "Media Player": {"Agora Multimedia Playback": "io.agora.media.player", "Aliyun Cloud Player": "com.aliyun.sdk.player", "Baidu Cloud Player": "com.baidu.cloud.media.player", "Baijiayun Cloud On Demand": "com.baijiayun.vod.sdk", "Bilibili ijkplayer": "tv.danmaku.ijk.media.player", "Bokecc Scene Video Cloud On Demand": "com.bokecc.sdk.mobile.play", "Brightcove Player": "com.brightcove.player", "Daniusdk Live Streaming Player": "com.daniulive.player", "Douban Audio Streamer": "dontknow", "Everyplay": "com.everyplay.Everyplay", "ExoMedia": "com.devbrackets.android.exomedia", "Gensee Interactive Player": "com.gensee.player", "Google ExoPlayer": "com.google.android.exoplayer2", "Hysteria Player": "dontknow", "JW Player": "com.jwplayer.jwplatform", "Jdcloud Live Streaming Player": "dontknow", "Kaltura Player": "com.kaltura.playkit", "LinkV Player": "com.linkv.player", "MHVideoPhotoGallery": "dontknow", "Movieous Player": "com.movieous.player", "Mudu Player": "com.mudu.player", "Netease Cloud Letter Player": "com.netease.neliveplayer", "Ooyala": "com.ooyala.android", "Polyv Cloud On Demand": "com.easefun.polyv.cloudclass", "Qiniu Player": "com.qiniu.pili.droid.player", "StreamLake-Player": "dontknow", "Tencent Player": "com.tencent.rtmp.player", "Twilio Live Player": "com.twilio.live.player", "VLC for Android": "org.videolan.vlc", "Veeplay Player": "com.veeplay.android", "Vitamio": "io.vov.vitamio", "Volcengine Player": "dontknow", "Yfcloud Player": "dontknow", "YinzCam Player": "dontknow", "Youtube Player": "com.google.android.youtube.player"}, "Real-time Audio and Video": {"100ms Audio and Video Communication": "live.hms.flutter", "3tee Audio and Video Communication": "dontknow", "AWS Chime": "com.amazonaws.services.chime", "Aestron Real-time Audio and Video": "dontknow", "Agora Audio And Video Communication": "io.agora.rtc", "Aliyun Audio And Video Communication": "com.alivc.rtc", "Ant Media": "io.antmedia", "AnyChat Audio and Video Communication": "com.bairuitech.anychat", "Anyrtc Audio And Video Communication": "org.anyrtc.rtmp", "Azure Communication Services": "com.azure.android.communication.calling", "Baidu Real-Time Audio And Video": "com.baidu.rtc", "Baijiayun BRTC": "com.baijiayun.brtc.sdk", "Bandwidth WebRTC": "com.bandwidth.webrtc", "Byteshop audio and video communication": "dontknow", "Classin Audio and Video Communication": "dontknow", "Cloudhub CyberLink Real-time Audio And Video": "dontknow", "Cloudroom SDK": "com.cloudroom.cloudroomvideosdk", "Cmsoft Cloud Real-Time Audio And Video": "dontknow", "CometChat": "com.cometchat.pro.android.chat", "Daily": "co.daily.sdk", "Discord RTC": "dontknow", "Dolby": "com.dolby.dap", "Duobeiyun Audio/Video Communication": "dontknow", "Dyte": "io.dyte.sdk", "Easemob Audio And Video Communication": "com.hyphenate.chat", "Ezviz VideoTalk": "com.ezviz.videotalk", "Gensee Interactive Audio And Video Communication": "com.gensee.rt", "Hh-Medic Video Asking": "dontknow", "Hst Video Communication": "dontknow", "Huawei Cloud Audio And Video Communication": "dontknow", "Ilivedata Real-Time Audio And Video": "dontknow", "Jdcloud Audio And Video Communication": "com.jdcloud.media.rtc", "Jpush JMRTC": "cn.jiguang.jmrtc.api", "Juphoon Audio And Video Communication": "com.juphoon.cloud.juphoon_cloud_sdk", "Kiswe": "dontknow", "LinkV Real-time Audio and Video": "com.linkv.rtc", "LiveKit": "io.livekit.android", "LiveSwitch RTC": "fm.liveswitch", "MirrorFly Calls": "com.mirrorfly.sdk", "Mlrtc Audio And Video Communication": "dontknow", "Mux-Real-Time": "dontknow", "NetEase Cloud Communication Audio and Video Communication": "com.netease.nimlib.sdk", "OpenWebRTC": "org.openwebrtc", "Pano Cloud Audio And Video Communication": "com.pano.rtc", "Phenix RTC": "net.phenixrts.sdk", "Photon Voice": "com.exitgames.photon.voice", "Qingmotech Cloud Audio/Video Communication": "dontknow", "Qiniu Audio And Video Communication": "com.qiniu.pili.droid.rtc", "Quanshi Cloud Conferencing": "dontknow", "QuickBlox": "com.quickblox.android.sdk", "Red5pro": "com.red5pro.sdk.core", "Rongcloud RongRTC": "io.rong.imlib", "Sendbird Calls": "com.sendbird.calls", "Shinevv Audio And Video Communication": "dontknow", "Sinch RTC": "com.sinch.android.rtc", "Snapcall": "com.snapcall.sdk", "StarRTC": "com.starrtc.starrtcsdk", "Stream Chat": "io.getstream.chat.android.client", "Talk-Cloud Audio And Video Communication": "dontknow", "Tencent TRTC": "com.tencent.trtc", "Twilio RTC": "com.twilio.video", "UCloud Audio and Video Communication": "com.ucloud.ulive.sdk", "Ucpaas Audio & Video": "com.ucpaas.ucapaas_sdk", "UserVoice": "com.uservoice.uservoicesdk", "Vidyo": "com.vidyo.VidyoClient", "Volcengine RTC": "dontknow", "Vonage OpenTok": "com.opentok.android.v2", "Voxeet": "com.voxeet.sdk", "WebRTC": "org.webrtc", "Whereby": "com.whereby.sdk", "Wowza": "com.wowza.gocoder.sdk", "Xylink Easy Link Audio/Video": "dontknow", "Youme Video Chat": "dontknow", "Yuntongxun Audio And Video Communication": "com.yuntongxun.ecsdk", "Zego Audio And Video Communication": "im.zego.zegoexpress", "Zego RoomKit": "dontknow", "Zoom Audio and Video Communication": "us.zoom.sdk", "Zoom Meeting": "us.zoom.sdk", "Zoom Video": "us.zoom.sdk"}, "Real-time Voice": {"10086 landline": "dontknow", "CC Game Real-Time Voice": "dontknow", "Qttaudio QttAudio": "dontknow", "Tencent Game Voice": "com.tencent.gcloud.voice", "Unity Vivox": "com.unity.services.vivox", "Youme Real-Time Voice": "dontknow", "Zego AudioRoom": "im.zego.audioroom"}, "Short Video": {"Aliyun Cloud Short Video": "com.alivc.shortvideo", "Baidu Cloud Short Video": "com.baidu.cloud.media.shortvideo", "Bokecc Scene Short Video": "dontknow", "Huawei Cloud Short Video": "dontknow", "Immomo Short Video": "dontknow", "Jdcloud Short Video": "com.jdcloud.media.shortvideo", "Jpush JVAAS": "cn.jiguang.jvaas.api", "Lansongai Short Video": "com.lansong.sdk", "Meishesdk Short Video": "dontknow", "Movieous Short Video": "com.movieous.shortvideo", "Qiniu Short Video": "com.qiniu.pili.droid.shortvideo", "Tencent Short Video": "com.tencent.liteav.shortvideo", "TikTok OpenSDK": "com.tiktok.opensdk.aweme", "Tiktok VideoKit for iOS": "dontknow", "Tusdk Short Video": "org.lasque.tusdk.video", "Upyun Cloud Short Video": "dontknow"}, "Video Engine": {"Gensee Interactive": "com.gensee.interactive", "Gensee Interactive Video On Demand": "dontknow", "IMG.LY VideoEditor": "ly.img.android.pesdk.video", "Rdsdk Video Editing": "dontknow", "Vesdk Video Engine": "dontknow", "Volcengine Engine VE": "dontknow"}, "Video Live Streaming": {"263 Cloud Broadcasting": "dontknow", "Agora Live Streaming": "io.agora.live", "Aliyun Cloud Push Streaming": "com.alivc.livepusher", "Baidu Cloud Live Link": "com.baidu.cloud.media.live", "Baijiayun Live": "com.baijiayun.live.sdk", "Bokecc Scene Live Video Push Streaming": "com.bokecc.sdk.mobile.live", "Bokecc Video Cloud Classroom": "dontknow", "Cloudhub Live Streaming": "dontknow", "Daniusdk Live": "com.daniulive.streaming", "Douyin Live": "dontknow", "Jdcloud Live Streaming": "com.jdcloud.media.live", "LinkV Live Broadcasting": "com.linkv.live", "Mudu Live": "com.mudu.live", "Netease Cloud Letter Live On Demand": "com.netease.neliveplayer", "Polyv Cloud Classroom": "com.easefun.polyv.cloudclass", "Polyv Interactive Learning": "dontknow", "Polyv Live Streaming Push Stream": "dontknow", "Polyv Live Streaming With": "dontknow", "Polyv Pure Video On-Demand": "dontknow", "Polyv Three Split Screen Open Broadcast": "dontknow", "Polyv Universal Live Streaming Scenario": "dontknow", "Qianxuecloud": "dontknow", "Qiniu Live Mic": "com.qiniu.pili.droid.streaming", "Qiniu Low Latency Live Streaming": "dontknow", "Rdsdk Live Streaming": "dontknow", "Sendbird Live": "com.sendbird.live", "Talk-Fun Live Streaming": "dontknow", "Talk-Fun Watch": "dontknow", "Tencent Live": "com.tencent.rtmp.live", "Tencent Live Mic": "dontknow", "Vhall Live": "com.vhall.live", "Yfcloud Live Streaming": "dontknow", "Zego LiveRoom": "im.zego.liveroom", "Zmengzhu Direct Broadcast": "dontknow"}, "VoIP": {"Plivo": "com.plivo.voice.android.sdk", "Twilio Programmable Voice": "com.twilio.voice"}}, "Development Tools": {"AR/VR": {"ARtoolkit5": "org.artoolkit.ar.base", "Baidu Dumix Ar": "com.baidu.ar", "Catchoom CraftAR": "com.catchoom.craftar", "DeepAR": "ai.deepar.deepar_example", "EasyAR": "cn.easyar.sdk", "Ezxr Insight AR": "dontknow", "Google ARCore": "com.google.ar.core", "Google VR Cardboard": "com.google.vr.sdk.base", "UtoVR": "dontknow", "Vuforia": "com.vuforia.engine", "Wikitude": "com.wikitude.architect"}, "Deep Linking": {"Adobe Experience Platform - Acquisition": "dontknow", "Bitly": "com.bitly.android.sdk", "Box DeepLink": "com.box.androidsdk.content", "Branch Deeplink": "io.branch.referral", "Button DeepLinkKit": "dontknow", "Coinbase": "com.coinbase.android.sdk", "Facebook App Links": "com.facebook.applinks", "Firebase DynamicLinks": "com.google.firebase.dynamiclinks", "Jpush MagicChain JMLink": "cn.jiguang.jmlink.api", "LinkedMe Deep Linking": "com.linkedme.android.sdk", "Umeng Smart Hyperlink": "com.umeng.socialize"}, "Development Frameworks": {"AAC Decoder": "dontknow", "Ali ARouter": "com.alibaba.android.arouter", "Alibaba Fastjson": "com.alibaba.fastjson", "AnalyticsKit": "dontknow", "Androidannotations": "org.androidannotations", "Apollo GraphQL": "com.apollographql.apollo", "Appcelerator Titanium": "ti.modules.titanium", "BFKit": "dontknow", "Booster": "com.didiglobal.booster", "Capacitor": "@capacitor/core", "Cordova": "org.apache.cordova", "CorePlot": "dontknow", "DTFoundation": "dontknow", "Facebook Bolts Framework": "com.facebook.bolts", "Facebook Yoga": "com.facebook.yoga", "Flutter": "io.flutter", "GCDWebServer": "dontknow", "HtmlCleaner": "org.htmlcleaner", "Ionic Framework": "@ionic/angular", "Ionic Keyboard": "@ionic-native/keyboard", "JDeferred": "org.jdeferred", "Liferay Screens": "com.liferay.mobile.screens", "Meituan Logan": "com.dianping.logan", "Microsoft Xamarin": "com.xamarin.forms", "Multiopen Hot Update": "dontknow", "OpenCV": "org.opencv", "PivotalCoreKit": "dontknow", "Quick Framework": "dontknow", "React Native": "com.facebook.react", "ReactiveX": "io.reactivex", "ReflectASM": "com.esotericsoftware.reflectasm", "ResearchKit": "dontknow", "SmartDeviceLink": "com.smartdevicelink.proxy", "Texture": "org.asyncdisplaykit", "Tuya Smart Life App": "com.tuya.smart", "Uni-App": "io.dcloud.feature.sdk"}, "Full Stack Integration": {"AWS Amplify": "com.amplifyframework", "Appwrite": "io.appwrite", "Backendless": "com.backendless", "BmobSDK": "cn.bmob.android.sdk", "CloudRail": "com.cloudrail.android.sdk", "FinClip Applet": "com.finogeeks.lib.finclip", "Firebase": "com.google.firebase", "Huawei Appgallery Connect": "com.huawei.agconnect", "Huawei HMS": "com.huawei.hms", "Kinvey": "com.kinvey.android", "MIT AppInventor": "edu.mit.appinventor", "NIFCloud mbaass": "com.nifcloud.mbaas.core", "Parse Platform": "com.parse", "Storyteller": "com.storyteller.ui"}, "Function Component": {"51trust Digital Health": "dontknow", "ABBYY Mobile Capture": "com.abbyy.mobile.capture.api", "APAddressBook": "dontknow", "ASM": "org.ow2.asm", "ATAppUpdater": "dontknow", "AWS Logs": "com.amazonaws.services.logs", "AboutLibraries": "com.mikepenz.aboutlibraries", "AdMuing Danmaku": "dontknow", "Adobe Experience Platform - Campaign Classic": "com.adobe.marketing.mobile", "Adobe Experience Platform - RulesEngine": "dontknow", "Adobe Experience Platform - UserProfile": "com.adobe.marketing.mobile.userprofile", "Airship In-App Automation": "com.urbanairship.automation", "Aliyun Crash Analysis": "dontknow", "Aliyun Logging Service": "com.aliyun.openservices.log.android", "Aliyun Performance Analysis": "dontknow", "Aliyun Remote Logging": "dontknow", "Android Chuck": "com.github.chuckerteam.chucker.api", "Android SQLiteAssetHelper": "com.readystatesoftware.sqliteassethelper", "Android Saripaar v2": "com.mobsandgeeks.saripaar", "Android Scanner": "dontknow", "Android System Bar Tint": "com.readystatesoftware.systembartint", "Android Viewbadger": "com.readystatesoftware.viewbadger", "AndroidPdfViewer": "com.github.barteksc.pdfviewer", "AndroidSVG": "com.caverock.androidsvg", "Androidplot": "com.androidplot.xy", "Animal Sniffer": "org.codehaus.mojo.animal_sniffer", "Antenna": "dontknow", "Apache Commons IO": "commons-io.commons-io", "AppUpdate": "com.king.app.updater", "Appiaries": "dontknow", "Appirater": "dontknow", "Appsquick Typhoon": "dontknow", "Artifex MuPDF": "com.artifex.mupdf.viewer", "Aspects": "dontknow", "Atomic Object Objection": "dontknow", "Bar Code Kit": "dontknow", "Basic HTTP Client for Java": "org.apache.httpcomponents", "Brother Print": "com.brother.ptouch.sdk", "Butter Knife": "com.jakewharton.butterknife", "Cartography": "dontknow", "CheckVersionLib": "com.allenliu.versionchecklib", "CocoaHTTPServer": "dontknow", "CocoaLumberjack": "dontknow", "CoconutKit": "dontknow", "Code Scanner": "com.budiyev.android.codescanner", "Codebots TBXML": "dontknow", "Com.Dd.Plist": "com.dd.plist", "Cordova sqlite storage plugin": "io.sqlc", "Couchbase": "com.couchbase.lite", "Cxense": "com.cxense.cxensesdk", "DBCamera": "dontknow", "DFCache": "dontknow", "DanmakuFlameMaster": "master.flame.danmaku", "Data Theorem TrustKit": "com.datatheorem.android.trustkit", "DateTools": "dontknow", "DevedUp FlickrKit": "dontknow", "Digitalocean": "com.digitalocean.api", "Dji": "dji.sdk.platform", "Dokit": "com.didichuxing.doraemonkit", "Douban SNSSharing": "com.douban.sdk.android", "Dynamic Tag Manager": "com.adobe.mobile.dtm", "Dynamsoft Barcode Reader": "com.dynamsoft.dbr", "EasyAndroidAnimations": "com.easyandroidanimations", "Enormego EGO Cache": "dontknow", "Epublib": "nl.siegmann.epublib", "Esoteric Software Kryo": "com.esotericsoftware.kryo", "Esoteric Software Minlog": "com.esotericsoftware.minlog", "Evernote": "com.evernote.android.sdk", "Expo": "expo.modules.core", "FC Current Location Geocode": "dontknow", "FCUUID": "dontknow", "FLAnimatedImage": "dontknow", "Face++ Megvii ID Quality Check": "dontknow", "Facebook Fresco": "com.facebook.fresco", "Fast Easy Mapping": "dontknow", "FastAdapter": "com.mikepenz.fastadapter", "FastCoding": "dontknow", "FastImageCache": "dontknow", "FasterXML Jackson": "com.fasterxml.jackson.core", "Fasttt Camera": "dontknow", "File Opener Plugin for Cordova": "dontknow", "FileKit": "dontknow", "FinClip Plug-in": "dontknow", "Firebase Functions": "com.google.firebase.functions", "Firebase Remote Config": "com.google.firebase.remoteconfig", "Flickr4Java": "com.flickr4java.flickr", "Flipper": "com.facebook.flipper", "Fotoapparat": "io.fotoapparat", "GEOSwift": "dontknow", "GHODictionary": "dontknow", "GPUImage": "jp.co.cyberagent.android.gpuimage", "GRDB": "dontknow", "GZIP": "java.util.zip", "Garmin Connect IQ": "com.garmin.android.connectiq", "Geniee": "jp.co.geniee.sdk.ads", "Genius-Android": "com.genius.android.sdk", "Giphy": "com.giphy.sdk", "Glide": "com.github.bumptech.glide", "Glide Transformations": "jp.wasabeef.glide.transformations", "Google APIs for Objective-C for REST": "dontknow", "Google Dagger": "com.google.dagger", "Google EasyPermissions": "pub.devrel.easypermissions", "Google Lib Phone Number": "com.google.i18n.phonenumbers", "Google Oboe": "com.google.oboe", "Google Promises": "com.google.android.gms.tasks", "Google Protocol Buffers": "com.google.protobuf", "Google Utilities": "com.google.android.gms.utils", "Google gson": "com.google.code.gson", "Greendao": "org.greenrobot.greendao", "Grpc": "io.grpc", "HH Router": "dontknow", "HTMLReader": "dontknow", "HanekeSwift": "dontknow", "Hermes": "com.facebook.hermes", "Hikvision Device Network": "com.hikvision.netsdk", "Hugo": "com.jakewharton.hugo", "HwCameraKit": "dontknow", "HwVideoKit": "dontknow", "IMG.LY PhotoEditor": "ly.img.android.pesdk", "IQ Keyboard Manager": "dontknow", "ImageBlurring": "dontknow", "InAppSettingsKit": "dontknow", "InflectorKit": "dontknow", "Invertase React Native Firebase": "io.invertase.firebase", "JT Localize": "dontknow", "JTwitter": "dontknow", "Jdcloud Application Publishing": "dontknow", "Joda-Time-Android": "net.danlew.joda", "JsBridge": "com.github.lzyzsd.jsbridge", "Jsoup": "org.jsoup", "Karte In App Messaging": "io.karte.android.inappmessaging", "Karte Variables": "io.karte.android.variables", "Karumi Dexter": "com.karumi.dexter", "Kdanmobile PDF": "com.kdanmobile.pdf.sdk", "KeepLayout": "dontknow", "Kingfisher": "dontknow", "Kite Print": "com.kite.sdk.print", "Kiwi": "dontknow", "Koushik Dutta ion": "com.koushikdutta.ion", "Kurento": "org.kurento", "Lebo Send Side": "dontknow", "LibRtmp Client for Android": "net.butterflytv.rtmp", "Libextobjc": "dontknow", "Linphone": "org.linphone.core", "Local Storage": "dontknow", "Logback": "ch.qos.logback", "Logger": "com.orhanobut.logger", "MIHCrypt": "dontknow", "MIKMIDI": "dontknow", "MMKV": "com.tencent.mmkv", "MT Migration": "dontknow", "Mailcore 2": "dontknow", "Mantle": "dontknow", "MapQuest Search Ahead": "dontknow", "Mars": "com.tencent.mars", "Metadata-Extractor": "com.drewnoakes.metadata-extractor", "Mi AIVS": "dontknow", "Mi Account Open Platform": "dontknow", "Mi App Store Check Update": "dontknow", "Minappcloud": "dontknow", "Mparticle (Adjust)": "dontknow", "Mparticle (AppsFlyer)": "dontknow", "Mparticle (Apptentive)": "dontknow", "Mparticle (Apptimize)": "dontknow", "Mparticle (BranchMetrics)": "dontknow", "Mparticle (Braze)": "dontknow", "Mparticle (Button)": "dontknow", "Mparticle (ComScore)": "dontknow", "Mparticle (Iterable)": "dontknow", "Mparticle (Leanplum)": "dontknow", "Mparticle (Localytics)": "dontknow", "Mparticle (Radar)": "dontknow", "Mparticle (UrbanAirship)": "dontknow", "Mudu Comment Library": "dontknow", "Multi-turbo": "dontknow", "MyScript InteractiveInk Runtime": "com.myscript.iink", "Naver ID": "com.nhn.android.naverlogin", "NineOldAndroids": "com.nineoldandroids.library", "Nuke": "dontknow", "OC Mockito": "dontknow", "OCHamcrest": "dontknow", "OCMock": "dontknow", "OCPsoft Prettyfaces": "com.ocpsoft.pretty.faces", "ObjectivePGP": "dontknow", "Objectivec-Flickr": "dontknow", "OneSpan Utilities": "com.onespan.sdk.utils", "Onfido Capture": "com.onfido.android.sdk.capture", "OpenCSV": "com.opencsv", "OpenTelemetry for Java": "io.opentelemetry.api", "Over-Scroll Support": "dontknow", "PCDN": "dontknow", "PINRemoteImage": "dontknow", "PSPDFKit": "com.pspdfkit.core", "PaperTrailLumberjack": "dontknow", "Parceler": "org.parceler", "PermissionsDispatcher": "com.github.hotchemi.permissionsdispatcher", "Photo Filters": "dontknow", "PhotoProcessing": "dontknow", "PhotoTweaks": "dontknow", "Pingan AI Intelligent Escort": "dontknow", "Pinterest PINCache": "dontknow", "Pocketchange": "dontknow", "PromiseKit": "dontknow", "Pull To Refresher": "com.yalantis.pulltorefresh", "Quick Nimble": "dontknow", "RMStore": "dontknow", "RT Language Textfield": "dontknow", "ReLinker": "com.getkeepsafe.relinker", "React Native Gesture Handler": "com.swmansion.gesturehandler", "React Native Permissions": "com.facebook.react.modules.permissions", "React Native Reanimated": "com.swmansion.reanimated", "React Native Screens": "com.swmansion.rnscreens", "React-native-device-info": "com.learnium.RNDeviceInfo", "ReactiveCocoa": "dontknow", "RegEx Categories": "dontknow", "Requery": "io.requery", "RootBeer": "com.scottyab.rootbeer", "RootTools": "com.stericson.RootTools", "RxFFmpeg": "com.github.wseef.rxffmpeg", "SBJson": "org.json.sb", "SDWebImage": "dontknow", "SGHTTP Request": "dontknow", "SQLite4Java": "com.almworks.sqlite4java", "SSKeychain": "dontknow", "SSZipArchive": "dontknow", "SVGeocoder": "dontknow", "Scandit": "com.scandit.barcodepicker", "Secure-Preferences": "com.securepreferences.library", "SimpleXML": "org.simpleframework.xml", "Sinch SMS": "com.sinch.android.rtc.messaging", "Sinovoice Intelligent Input Method": "dontknow", "Slf4j": "org.slf4j", "SmartCropper": "com.github.pqpo.smartcropperlib", "SoLoader": "com.facebook.soloader", "Soomla Keeva": "com.soomla.keeva", "Square Moshi": "com.squareup.moshi", "Square Picasso": "com.squareup.picasso", "Square Seismic": "com.squareup.seismic", "Square TimesSquare": "com.squareup.timessquare", "String Fog": "com.github.megatronking.stringfog", "Sweet Alert Dialog": "cn.pedant.<PERSON>", "Swift Date": "dontknow", "Swinject": "dontknow", "SwipeBackLayout": "me.imid.swipebacklayout.lib", "Sync": "dontknow", "TCBlobDownload": "dontknow", "TGPA-Lite": "dontknow", "TPKeyboardAvoiding": "dontknow", "Tag Group": "me.gujun.android.taggroup", "Tencent Browsing Service TBS": "com.tencent.smtt.sdk", "Tencent Data Viva": "dontknow", "ThreeTen Android Backport": "com.jakewharton.threetenabp", "Timber": "timber.log", "Tiny Image Compression": "com.zxy.tiny.core", "TokenAutoComplete": "com.tokenautocomplete", "Trello RxLifecycle": "com.trello.rxlifecycle", "Twitter Text": "com.twitter.twittertext", "Uber AutoDispose": "com.uber.autodispose", "Umeng Common": "com.umeng.commonsdk", "Universal Image Loader": "com.nostra13.universalimageloader", "VLC Android": "org.videolan.vlc", "Volcengine Fiction": "dontknow", "Volcengine Flames": "dontknow", "Volcengine Hotfix": "dontknow", "Volcengine ImageX": "dontknow", "Volcengine Mira Plugin Service": "dontknow", "Volcengine Secure Keyboard": "dontknow", "WPMediaPicker": "dontknow", "WebViewJavascriptBridge": "com.github.lzyzsd.jsbridge.BridgeWebView", "WordPress": "org.wordpress.android", "WordPress Analytics": "org.wordpress.android.analytics", "WordPress Aztec Editor": "org.wordpress.aztec", "WordPress XML-RPC Framework": "org.wordpress.android.xmlrpc", "XExtensionItem": "dontknow", "XMPPFramework": "dontknow", "XUI": "com.xuexiang.xui", "Xiaojukeji Open Platform": "dontknow", "Xutils3": "org.xutils", "YYEVA": "com.yy.yeva_android", "Yalantis uCrop": "com.yalantis.ucrop", "Yfuyou Doctor": "dontknow", "ZBar": "me.dm7.barcodescanner.zbar", "Zebra RFID Reader": "com.zebra.rfid.api3", "Zendesk Unity": "dontknow", "Zip4j": "net.lingala.zip4j", "ZipArchive": "dontknow", "ZipKit": "dontknow", "ZipUtilities": "dontknow", "Znds Self-Updating": "dontknow"}, "Identity Verification": {"51yixun Cloud One-Click Login": "dontknow", "AFNetworking OAuth2 Manager": "dontknow", "AWS Auth": "com.amplifyframework.auth", "AWS Cognito": "com.amazonaws.mobile.client.aws-android-sdk-mobile-client", "Acuant": "com.acuant.mobilesdk", "Aliyun Cloud One-Click Login": "com.aliyun.dypns", "Aliyun Financial Grade Real Person Authentication": "dontknow", "Aliyun Real Person Authentication": "dontknow", "AriadNext IDCheck": "com.ariadnext.idcheck", "Auth0 Identity Authentication": "com.auth0.android", "Authing Identity Authentication": "cn.authing.guard", "Baidu Face Recognition": "com.baidu.idl.face.platform", "Baidu Intelligent Cloud One-Click Login": "dontknow", "Bjca Identity Verification": "dontknow", "Chuanglan Flash Test": "dontknow", "Cloudwalk Detection Recognition": "dontknow", "Ctyun Cloud One-Click Login": "dontknow", "DocuSign": "com.docusign.android.sdk", "Emay One-Click Login": "dontknow", "Esignchina E-signature": "cn.esign.sdk.android", "Firebase Authentication": "com.google.firebase.auth", "Foursquare OAuth": "com.foursquare.android.sdk", "GeeTest Behavioral Captcha": "com.geetest.sdk.gtc", "GeeTest Behavioral Verification": "dontknow", "GeeTest Deep Captcha Pro": "dontknow", "GeeTest Device Verification": "dontknow", "GeeTest Invisible Captcha": "dontknow", "Geyan": "dontknow", "Google SignIn": "com.google.android.gms.auth.api.signin", "Grab GrabId Partner": "com.grab.grabid.partner", "ID Scan ID Parser": "com.idscan.sdk.parser", "IDnow Auto Ident": "de.idnow.sdk", "IDnow Video Ident": "de.idnow.sdk", "Incognia Auth": "com.incognia.android.sdk.authentication", "Jpush JVerification": "cn.jiguang.verifysdk", "Jumio Auth": "com.jumio.auth", "Jumio BAM Checkout": "com.jumio.bam.checkout", "Kount": "com.kount.android.sdk", "Linkface Face Recognition": "com.linkface.sdk", "Loginradius": "com.loginradius.android.sdk", "Microblink": "com.microblink", "Microsoft ADAL": "com.microsoft.aad.adal", "MobTech Mobile Verification": "com.mob.mobverify", "Nimbus JOSE + JWT": "com.nimbusds.jose", "OAuthSwift": "dontknow", "Okta Identity Authentication": "com.okta.android.sdk", "OneSpan Sign": "com.onespan.android.sdk.signature", "OpenID AppAuth": "net.openid.appauth", "Passbase": "com.passbase.passbase_sdk", "PingOne Verify": "com.pingidentity.pingone.verify.sdk", "SAP CDC（Gigya）": "com.gigya.android.sdk", "Shufti Pro": "com.shuftipro.sdk", "Sinch Verification": "com.sinch.verification", "Sinopayment Authentication": "dontknow", "Spot Hero Email Validator": "dontknow", "Spotify": "com.spotify.android.appremote", "SubMail One-click Login": "dontknow", "SuperID Identity Authentication": "com.superid.sdk", "T1 Autograph": "dontknow", "Tencent Cloud Number Authentication": "com.tencent.cloud.number", "Tencent Face Check Body": "com.tencent.cloud.face", "TransUnion TruValidate Device Risk": "com.transunion.truvalidate.sdk", "Umeng Smart Authentication": "com.umeng.umverify", "Unify Id": "com.unifyid.sdk", "Upyun Cloud One-Click Login": "dontknow", "VENTouchLock": "dontknow", "Veriff Auth": "com.veriff.sdk.android", "Volcengine Authentication": "dontknow", "Wlwx (Future Wireless)": "dontknow", "Yahoo TDOAuth": "dontknow", "Yandex Login": "com.yandex.authsdk", "Yuntongxun Cloud One-Click Login": "dontknow", "Zoloz Identity Authentication": "com.zoloz.android.sdk"}, "Language Processing": {"AWS Comprehend": "com.amazonaws.services.comprehend", "AWS Translate": "com.amazonaws.services.translate", "Applanga": "com.applanga.android", "Baidu Translation": "com.baidu.translate.api", "Google MLKit Translate": "com.google.mlkit.translate", "Sinovoice Machine Translation MT": "dontknow", "Sinovoice Semantic Recognition NLU": "dontknow", "Youdao Natural Language Translation": "com.youdao.cloud.translation", "Zhiling Speaking Review": "dontknow"}, "Location Information": {"AWS Location": "com.amazonaws.services.location", "Adobe Experience Platform - Places": "com.adobe.marketing.mobile.places", "Airship Location": "com.urbanairship.location", "Autonavi Location": "com.amap.api.location", "Baidu Location": "com.baidu.location", "Blesh Location": "com.blesh.sdk", "Bluedot": "com.bluedotio.android.sdk", "Brtbeacon Technology Indoor Location": "dontknow", "Colocator": "com.colocator.sdk", "ESRI ArcGIS": "com.esri.arcgis.android.app", "FCIPAddressGeocode": "dontknow", "Firebase geofire": "com.firebase.geofire.android", "Foursquare Location Broker": "com.foursquare.pilgrim", "Google Places": "com.google.android.gms.location.places", "GroundTruth Location": "com.xad.sdk", "Herow": "com.herow.sdk", "HyperTrack": "com.hypertrack.sdk", "INTULocationManager": "dontknow", "Phunware Location": "com.phunware.android.sdk.location", "Plotprojects": "com.plotprojects.retail.android", "ProxSee": "dontknow", "Proxama TapPoint": "com.proxama.tappoint.sdk", "Proximi.Io": "io.proximi.proximiiolibrary", "Radar Location": "io.radar.sdk", "Rakuten Ready": "com.rakuten.ready.sdk", "Resono": "dontknow", "Roam": "com.roam.android", "Skyhook": "com.skyhookwireless.skyhook", "Skyhook Location": "com.skyhookwireless.wps", "Smart Location Library": "io.nlopez.smartlocation", "Swift Location": "dontknow", "Ubudu": "com.ubudu.sdk", "Vectaury Location": "com.vectaury.sdk"}, "Login and Share": {"Dingding Open Platform": "com.dingtalk.open.sdk", "Douyin Open Platform": "com.douyin.open.sdk", "Facebook Messenger Share Kit": "com.facebook.messenger", "Facebook Share Kit": "com.facebook.share", "Feishu Login": "com.larksuite.oapi", "Immomo One-Click Login": "dontknow", "Jpush Share JShare": "cn.jiguang.share.android.api", "Ksyun One-Click Login": "dontknow", "LinkedMe Flash Login": "dontknow", "Lmobile One-Click Login": "dontknow", "MobTech ShareSDK": "cn.sharesdk.framework", "Sogou Login": "dontknow", "U8SDK-Login and Payment": "dontknow", "Umeng Social Sharing": "com.umeng.socialize"}, "Machine Learning": {"AWS Machine Learning": "com.amazonaws.services.machinelearning", "AWS SageMaker": "com.amazonaws.services.sagemaker.runtime", "Baidu Easyedge": "com.baidu.easyedge", "TensorFlow Lite": "org.tensorflow.lite"}, "Message Pushing": {"AWS SNS": "com.amazonaws.services.sns", "Adobe Experience Platform - MobileServices": "com.adobe.marketing.mobile", "Aerogear Push Notifications": "org.jboss.aerogear.android.push", "Airship Notifications": "com.urbanairship.push", "Aliyun Cloud Messaging Push": "com.aliyun.ams.emas", "Baidu Cloud Push": "com.baidu.android.pushservice", "Baidu Huitui": "dontknow", "Batch Push": "com.batch.android", "Boxcar": "dontknow", "Catapush": "com.catapush.library", "CleverPush": "de.cleverpush.cleverpush_flutter", "Cordova Local Notifications": "de.appplant.cordova.plugin.local-notification", "FX Notifications": "dontknow", "Firebase Cloud Messaging": "com.google.firebase.messaging", "Getui": "com.igexin.sdk", "Greenrobot EventBus": "org.greenrobot.eventbus", "GrowthPush": "com.growthpush", "Honor Push": "com.hihonor.push.sdk", "Huawei Push": "com.huawei.hms.push", "Immomo Message Push": "dontknow", "Iterable": "com.iterable.java.sdk", "JPush": "cn.jpush.android.api", "Karte Remote Notification": "io.karte.android.notifications", "Kumulos": "com.kumulos.android", "MPMessagePack": "org.msgpack.core", "Mi Push": "com.xiaomi.mipush.sdk", "Mklink Push": "dontknow", "Mob MobPush": "com.mob.pushsdk", "Notificare Push Lib": "re.notifica.push.gcm", "OneSignal": "com.onesignal", "OneSpan Notification": "com.onespan.sdk.notification", "Oppo Push": "com.heytap.msp.push", "Oracle Responsys Mobile": "com.responsys.android.sdk", "Pushbots": "com.pushbots.push", "Pusher": "com.pusher.client", "Pushwoosh": "com.pushwoosh.pushwoosh", "Pushy": "me.pushy.sdk", "RMC Push": "dontknow", "Rongcloud Push": "io.rong.push", "Tencent Mobile Push": "com.tencent.android.tpush", "TwinPush": "com.twinpush.sdk", "Umeng Message Push": "com.umeng.message", "Vivo Push": "com.vivo.push", "Volcengine Mobile Push": "dontknow", "WonderPush": "com.wonderpush.sdk", "Zendesk Messaging": "com.zendesk.sdk.messaging"}, "Near Field Communication": {"AltBeacon": "org.altbeacon.beacon", "Amazon Fling": "com.amazon.whisperplay.fling", "Aruba Networks": "com.arubanetworks.meridian", "BLE Beacon": "dontknow", "Beaconstac": "com.beaconstac.android-sdk", "Beaconwatcher": "dontknow", "Brtbeacon Technology Beacon": "dontknow", "Connect SDK": "com.connectsdk", "Google Cast": "com.google.android.gms.cast.framework", "Google Nearby": "com.google.android.gms.nearby", "Kontakt": "com.kontakt.sdk.android.ble", "PebbleKit": "com.getpebble.android.kit", "Polar": "com.polar.sdk.api", "Sensorberg": "com.sensorberg.sdk", "Soti MobiControl": "net.soti.mobicontrol", "Square Reader": "com.squareup.sdk.reader", "Swirl Network Proximity": "dontknow", "Tourmaline": "dontknow", "Yunba": "io.yunba.android.sdk", "Zebra PrintConnect": "com.zebra.printconnect"}, "SMS and Email": {"AWS SES": "com.amazonaws.services.simpleemail", "Jpush SMS JSMS": "cn.jiguang.sms.android.api", "Mob SMSSDK": "cn.smssdk"}, "Scan Recognition": {"ACRCloud": "com.acrcloud.rec.sdk", "AWS Textract": "com.amazonaws.services.textract", "Anyline": "io.anyline.android", "Arcsoft Face Recognition": "com.arcsoft.face", "Baidu Text Recognition": "com.baidu.ocr.sdk", "BlinkInput Blink OCR": "com.microblink.blinkinput", "Card.Io": "io.card.payment", "Deepfinch Face Recognition": "dontknow", "Deepfinch OCR": "dontknow", "Deepfinch Wise Loss Determination": "dontknow", "Dynamsoft Label Recognizer": "com.dynamsoft.dlr", "Eidtokencloud ID Reading": "dontknow", "Eidtokencloud Passport Pass Recognition": "dontknow", "Face++ Megvii Face Recognition": "com.megvii.facepp", "Facetec Face Recognition": "com.facetec.sdk", "Google MLKit Barcode": "com.google.mlkit.vision.barcode", "Google MLKit DigitalInk": "com.google.mlkit.vision.digitalink", "Google MLKit Face Detection": "com.google.mlkit.vision.face", "Google MLKit TextRecognition": "com.google.mlkit.vision.text", "Hisign Face Recognition": "dontknow", "Huawei Cloud Text Recognition": "dontknow", "ID Scan Camera Scanning": "com.idscan.sdk.camera", "ID Scan Multiscan": "com.idscan.sdk.multiscan", "ID Scan Passport MRZ Scanner": "com.idscan.sdk.mrz", "Intsig OCR": "com.intsig.camscanner", "Itext7": "com.itextpdf", "Itruscloud Real Name Authentication": "dontknow", "Linkface OCR": "dontknow", "Microblink BlinkCard": "com.microblink.blinkcard", "Microblink BlinkID": "com.microblink.blinkid", "Microblink BlinkReceipt": "com.microblink.blinkreceipt", "Microblink PDF417": "com.microblink.pdf417", "Microblink Photo Pay": "com.microblink.photopay", "Milibris PDF Reader": "dontknow", "OneSpan Image Scanner": "com.onespan.sdk.image", "PDF417.mobi": "com.pdf417.mobi", "PDFTron": "com.pdftron", "Regula Document Reader": "com.regula.documentreader.api", "Scanbot OCR": "io.scanbot.sdk", "Seetatech Face Recognition": "dontknow", "Sinovoice Face Recognition AFR": "dontknow", "Sinovoice Handwriting Recognition HWR": "dontknow", "Sinovoice Text Recognition OCR": "dontknow", "Slyce": "com.slyce.sdk", "Youdao Optical Character Recognition": "com.youdao.ocr", "ZXing Barcode Scanner": "com.google.zxing", "Zebra Scanner": "com.zebra.scannercontrol"}}, "Game": {"Game Data Analysis": {"Baidu Game Analytics": "dontknow", "Cocos Analytics": "com.cocos.analytics", "DeltaDNA": "com.deltadna.android.sdk", "Game of Whales": "com.gameofwhales.sdk", "GameAnalytics": "com.gameanalytics.sdk", "GameCenterManager": "dontknow", "Huya Data Interoperability": "dontknow", "Polljoy": "com.polljoy", "Reyun Game Analysis": "com.reyun.sdk.analytics", "TalkingData Game Analytics": "com.tendcloud.gametend", "Thinking Analytics": "cn.thinkingdata.android", "Umeng Game Statistics": "com.umeng.game", "Yerdy": "dontknow"}, "Game Engine": {"Chipmunk2d": "dontknow", "Cocos2D-X": "org.cocos2dx.lib", "GameSparks": "com.gamesparks", "LibGDX": "com.badlogic.gdx", "Solar2D": "com.coronalabs", "Sparrow": "dontknow", "Unity": "com.unity3d.player", "Unreal Engine 4": "com.epicgames.ue4"}, "Game Interface": {"Unity-Webview": "net.gree.unitywebview"}, "Game Platform": {"86shouyou": "dontknow", "Cheetahfun Game": "dontknow", "Flamingo-Inc Games": "dontknow", "Heroic Labs": "com.heroiclabs.nakama", "Kongregate": "com.kongregate.android.api", "Longtugame": "dontknow", "Mi App Syndication": "dontknow", "Microsoft PlayFab": "com.playfab", "TapTap Games": "com.taptap.sdk", "WeGame": "com.wegame.sdk", "Yodo1": "com.yodo1.android.sdk"}, "Game Services": {"Fast Explosion Developer Platform": "dontknow", "Google Play Game Services": "com.google.android.gms.games", "Huawei Cloud Game Service": "com.huawei.hms.jos", "Mi Game Service": "com.xiaomi.gamecenter.sdk", "Mklink Game Service": "dontknow", "Oppo Game Publishing And Operations": "dontknow", "QuickGame": "dontknow", "QuickSDK": "com.quicksdk.android", "Vivo Game Publishing And Operations": "dontknow", "Xiaoyaoyou Cloud Games": "dontknow"}}, "Integration Services": {"Application Full Chain Service": {"Applicaster Zapp": "com.applicaster.zapp", "Baidu Smart Applet": "com.baidu.smartapp", "BrainCloud": "com.braincloud.android", "FollowAnalytics": "com.followanalytics.android", "Judo": "com.judopay.android", "LaunchDarkly": "com.launchdarkly.android-client-sdk", "Microsoft App Center": "com.microsoft.appcenter", "ShepHertz App42": "com.shephertz.app42.paas.sdk.android", "TLIndexPathTools": "dontknow", "Volcengine Mobile R&D Platform": "dontknow", "Volcengine Multimedia Center": "dontknow", "Workspace ONE": "com.airwatch.android.sdk"}, "Business Communication": {"Airship Message Center": "com.urbanairship.messagecenter", "Freshworks Hotline": "com.freshworks.hotline", "Indigitall": "com.indigitall.android.sdk", "Intercom": "io.intercom.android", "Microsoft Yammer": "com.yammer.android", "Qualaroo": "com.qualaroo.android", "Zendesk Sunshine Conversations": "io.sunshine.conversations.android.sdk"}, "E-commerce Platform": {"Alibaba Baichuan E-Commerce": "com.alibaba.baichuan.android.trade", "RichRelevance": "com.richrelevance.android.sdk", "Shopify Buy": "com.shopify.buy3", "Voucherify": "io.voucherify.android.sdk", "Xiaoe-Tech E-Commerce Transaction": "dontknow", "Youzan App Open Store": "com.youzan.android.sdk", "Zomato order": "com.zomato.sdk.order"}, "Education Service": {"Studyplus": "jp.studyplus.android"}, "Instant Messaging": {"AWS Connect": "com.amazonaws.services.connect", "Agora Voice Communication": "io.agora.rtm", "Airship Live Chat": "dontknow", "AnyRTC Instant Messaging": "org.anyrtc.im", "Applozic IM": "com.applozic.mobicomkit", "Baijiayun Cloud Real-Time Messaging": "com.baijiayun.im.sdk", "Cmsoft Cloud Instant Messaging": "dontknow", "Easemob Customer Service Cloud": "com.hyphenate.helpdesk", "Easemob Instant Messaging": "com.hyphenate.chat", "Ignite Realtime JXMPP": "org.jivesoftware.smack", "Immomo Instant Messaging": "dontknow", "Infobip": "com.infobip.mobile.messaging.api", "Jpush IM JMessage": "cn.jiguang.im.android.api", "Kustomer Chat": "com.kustomer.chat.android", "LeanCloud IM": "cn.leancloud.im", "LinkV Instant Messaging": "com.linkv.im", "LiveChat": "com.livechatinc.inappchat", "Maximtop Topology IM": "dontknow", "Meiqia Customer Service System": "com.meiqia.core", "MirrorFly Chat": "com.mirrorfly.sdk", "Netease Instant Messaging": "com.netease.nimlib.sdk", "Photon Chat": "com.exitgames.photon.chat", "PubNub Real-time Communication": "com.pubnub.api", "Qiniu Instant Messaging": "com.qiniu.pili.droid.im", "Quicksdk QuickSDK Customer Service": "dontknow", "Rongcloud Instant Messaging": "io.rong.imlib", "Sendbird Chat": "com.sendbird.android", "Smack": "org.jivesoftware.smack", "Sobot Online Customer Service": "com.sobot.chat", "Startalk": "dontknow", "Telegram Passport": "org.telegram.passport", "Tencent Instant Messaging": "com.tencent.imsdk", "Thinkive Video Customer Service": "dontknow", "Twilio Chat Client": "com.twilio.chat", "Udesk Customer Service System": "cn.udesk", "V5 Customer Service": "dontknow", "Wecloud Audio And Video Communication": "dontknow", "WhatsApp": "com.whatsapp", "Yixin IM": "im.yixin", "Youme Instant Messaging": "dontknow", "Yuntongxun Cloud Instant Messaging": "com.yuntongxun.ecsdk.im", "Yuntongxun Qimo": "dontknow", "Zego Instant Messaging": "im.zego.zim"}, "Intelligent Chat": {"AIHelp": "net.aihelp.init", "AWS Lex": "com.amazonaws.services.lex", "Ntalker Customer Service": "com.ntalker.sdk.android", "Sobot Intelligent Customer Service": "com.sobot.chat.api", "Zendesk Answer Bot": "com.zendesk.sdk.answerbot"}, "Map Services": {"AMapLocation lieying": "dontknow", "Airbnb Mapview": "com.airbnb.android.airmapview", "Autonavi Map": "com.amap.api.maps", "Baidu Map": "com.baidu.mapapi", "Baidu Panorama Map": "com.baidu.lbsapi.panoramaview", "Brtbeacon Technology Indoor Map": "dontknow", "CARTO Mobile": "com.carto.mobile.sdk", "Google Maps": "com.google.android.gms.maps", "Here Maps": "com.here.android.mpa.mapping", "Huawei Location Service": "com.huawei.hms.location", "Huawei Map": "com.huawei.hms.maps", "Insiteo": "com.insiteo.lbs.sdk", "Kakao Maps": "net.daum.mf.map.api.net.daum.mf.map.api", "Karhoo": "com.karhoo.sdk.api", "LocusLabs Maps": "com.locuslabs.sdk.llmaps", "MapQuest Maps": "com.mapquest.mapping", "Mapbox": "com.mapbox.mapboxsdk", "Maps Indoors": "com.mapspeople.mapsindoors", "Mapsforge Maps": "org.mapsforge.map.android", "Mapzen": "com.mapzen.android.sdk", "Phunware Mapping": "com.phunware.android.sdk.mapping", "SKMaps": "com.skobbler.ngx", "Steerpath": "com.steerpath.sdk", "Sygic Maps & Navigation developer kit": "com.sygic.sdk", "Szshuwei Indoor Positioning": "dontknow", "Tencent Maps": "com.tencent.map.geolocation", "Yandex Map Kit": "com.yandex.mapkit"}, "Payment": {"2C2P PGW SDK": "com.ccpp.pgw.sdk", "Adapty": "com.adapty.push", "Adyen": "com.adyen.checkout", "Adyen 3DS2": "com.adyen.checkout.adyen3ds2", "Alipay": "com.alipay.sdk", "Amazon In-App Purchasing": "com.amazon.device.iap", "Anjlab Android In-App Billing v3": "com.anjlab.android.iab.v3", "AnyPay": "jp.anyinc.android.anypay", "AppHud": "com.apphud.sdk", "BeeCloud": "cn.beecloud", "CardFlight": "com.getcardflight.models", "Citruspay": "com.citrus.sdk", "Eu.Epay": "dontknow", "Ingenico": "com.ingenico.connect.gateway", "Ipaynow": "com.ipaynow.sdk", "Jd Payment": "com.jd.pay", "Jianmi Ping++": "com.pingplusplus.android", "Lianlianpay": "com.lianlianpay.android.sdk", "MercadoPago Devices": "com.mercadopago.android.px.devices", "Omise": "co.omise.android", "PayPal": "com.paypal.android.sdk.payments", "PayPal Braintree": "com.braintreepayments.api", "PayPal Here": "com.paypal.merchant.sdk", "PayPal Venmo": "com.venmo.android.sdk", "PayPal zettle": "com.zettle.sdk.android", "PayU": "com.payu.india.sdk", "Paymentwall": "com.paymentwall.android.sdk", "Paytm All-in-One": "com.paytm.pgsdk", "Plaid Link": "com.plaid.link.sdk", "QQ Wallet": "com.tencent.mobileqq.openpay", "Qonversion": "com.qonversion.android.sdk", "Razorpay": "com.razorpay", "Recurly": "com.recurly.android.sdk", "RevenueCat": "com.revenuecat.purchases", "Stripe": "com.stripe.android", "SumUp": "com.sumup.merchant.api", "USAePay": "com.usaepay.sdk", "Umfintech Payment": "dontknow", "Unionpay": "com.unionpay", "Visa Cardinal Mobile": "com.cardinalcommerce.cardinalmobilesdk", "Visa Checkout": "com.visa.checkout.android.sdk", "WePay": "com.wepay.android.sdk", "Wirecard Payment": "com.wirecard.payment"}, "Real-time Messaging": {"Ably": "io.ably.lib.realtime", "Agora Cloud Signaling (real-time Messaging)": "io.agora.rtm", "Pusher Channels": "com.pusher.client"}, "Route Navigation": {"Autonavi Navigation": "com.amap.api.navi", "Baidu Navigation": "com.baidu.navisdk.adapter", "Baidu Yingyan": "com.baidu.trace.api", "Huawei Navi Kit": "com.huawei.hms.navi", "Kudan": "eu.kudan.ar", "MapQuest Navigation": "com.mapquest.android.ace", "Pathsense": "com.pathsense.android.sdk"}, "Smart Space": {"AreaMetrics": "com.areametrics.areametricssdk", "Bluecats": "com.bluecats.sdk", "Estimote Fleet Management": "com.estimote.sdk", "Estimote Proximity": "com.estimote.proximity", "Ezeeworld Neerby": "com.ezeeworld.neerby.sdk", "Fourquare Pilgrim": "com.foursquare.pilgrim", "Gimbal": "com.gimbal.android", "Star Micronics Cloud": "com.starmicronics.stario"}, "Social Networking": {"Amazon Login": "com.amazon.identity.auth.device.api", "Facebook": "com.facebook.android", "Facebook Login": "com.facebook.login", "Facebook SDK for iOS": "dontknow", "Kakao Social": "com.kakao.sdk.auth", "Line": "com.linecorp.linesdk", "Linkedin Mobile": "com.linkedin.android.mobilesdk", "Lobi": "com.kayac.lobi.sdk", "QQ": "com.tencent.mobileqq.sdk", "Schibsted": "com.schibsted.account.android.sdk", "Sina Weibo": "com.sina.weibo.sdk", "Snapchat Kit": "com.snapchat.kit.sdk", "TikTok LoginKit for iOS": "dontknow", "Tumblr": "com.tumblr.jumblr", "Twitter": "com.twitter.sdk.android", "Twitter4J": "twitter4j", "VKontakte": "com.vk.sdk", "Wechat": "com.tencent.mm.opensdk"}}, "Operations and Maintenance Service": {"Cloud Infrastructure Service": {"AWS API Gateway": "com.amazonaws.mobileconnectors.apigateway", "AWS Auto Scaling": "com.amazonaws.services.autoscaling", "AWS EC2": "com.amazonaws.services.ec2", "AWS ElasticLoadBalancing": "com.amazonaws.services.elasticloadbalancing", "AWS Lambda": "com.amazonaws.services.lambda", "AWS SQS": "com.amazonaws.services.sqs"}, "Cloud Storage": {"AWS S3": "com.amazonaws.services.s3", "Aliyun Cloud Object Storage": "com.aliyun.oss.android.sdk", "Alooma": "com.alooma.android.sdk", "Dropbox": "com.dropbox.core", "Firebase Cloud Storage": "com.google.firebase.storage", "Huawei Cloud Object Storage": "com.obs.services", "Ksyun Object KS3": "com.ksyun.ks3.android.sdk", "Microsoft Azure": "com.microsoft.azure.storage.android", "Qiniu Cloud Storage": "com.qiniu.android.storage", "Tencent Object Storage": "com.tencent.cos.xml", "UCloud Object Storage": "cn.ucloud.ufile", "Wangsu Object Storage": "dontknow"}, "Consent Management Platform": {"AppConsent CMP": "io.appconsent.sdk", "Didomi": "io.didomi.sdk", "GDPRDialog": "com.github.MorochoRochaDarwin.form-android-sdk-dialog-gdpr", "Google Mobile Ads Consent": "com.google.android.ump", "Quantcast Choice": "com.quantcast.choice.core", "Sourcepoint CMP": "com.sourcepoint.cmplibrary"}, "Customer Experience": {"AWS Pinpoint": "com.amazonaws.mobile.client.aws-android-sdk-mobile-client", "Adobe Audience Manager": "com.adobe.marketing.mobile.audience", "Airship": "com.urbanairship", "Aliyun Mobile User Feedback": "com.aliyun.ams.feedback", "Applause": "com.applause.android", "Apptentive": "com.apptentive.android.sdk", "Bloomreach Exponea": "com.exponea.sdk", "Braze Appboy": "com.appboy", "Commanders Act TagCommander": "com.commandersact.tcsdk", "Freshworks Mobihelp": "com.freshdesk.mobihelp", "FullStory": "com.fullstory.instrumentation", "Glance": "com.glance.android.sdk", "Glympse": "com.glympse.android.api", "HappyFox Helpstack": "com.happyfox.helpstack.sdk", "Helpshift": "com.helpshift.android.sdk", "LeanCloud Feedback": "cn.leancloud.feedback", "Liquid": "com.liquid.android.sdk", "Localz Driver": "com.localz.driver.sdk", "Lyft": "com.lyft.lyftbutton", "Moengage": "com.moengage.sdk", "Netease sevenfish customer service": "com.qiyukf.unicorn", "Phunware Mobile Engagement": "com.phunware.android.sdk.engagement", "Qiscus": "com.qiscus.sdk.chat.core", "Radius Networks FlyBuy": "com.radiusnetworks.flybuy.sdk", "Sailthru": "com.sailthru.mobile.sdk", "Sendbird Desk": "com.sendbird.desk.android", "Sentiance": "com.sentiance.sdk", "SessionM": "com.sessionm.api", "SurveyMonkey": "com.surveymonkey.android.sdk", "TapResearch": "com.tapr.sdk", "Tingyun": "com.networkbench.agent.impl", "Usabilla": "com.usabilla.sdk.ubform", "Userzoom": "com.userzoom.sdk", "Vonage Nexmo Client": "com.nexmo.client", "WalkMe Abbi": "com.walkme.android.sdk", "WebEngage": "com.webengage.sdk.android", "Zendesk": "com.zendesk.sdk", "Zendesk Chat": "com.zendesk.sdk.chat"}, "Data Management Platform": {"Adobe Experience Platform - Audience": "com.adobe.marketing.mobile.audience", "Adobe Experience Platform - Campaign": "com.adobe.marketing.mobile.campaign", "Alfresco": "org.alfresco.mobile.android.sdk", "Kofax": "com.kofax.mobile.sdk", "Lotame": "com.lotame.android.sdk", "Particle": "io.particle.android.sdk", "Phunware Core": "com.phunware.android.sdk.core", "PostHog": "com.posthog.android", "Skyhook Context": "com.skyhookwireless.context", "StartApp - TrueNet Network and Speed Info": "com.startapp.network.truenet", "Tealium (React Native)": "com.tealium.reactnative", "Treasure Data": "com.treasuredata.android.sdk", "Validic": "com.validic.mobile", "Vectaury CMP": "dontknow", "WordPress": "org.wordpress.android.editor"}, "Database": {"AWS DynamoDB": "com.amazonaws.services.dynamodbv2", "AWS SimpleDB": "com.amazonaws.services.simpledb", "Firebase Database": "com.google.firebase.database", "Firebase Firestore": "com.google.firebase.firestore", "ObjectBox": "io.objectbox", "Realm Database": "io.realm", "SQL Cipher": "net.sqlcipher.database", "SnappyDB": "com.snappydb", "WCDB Database Framework": "com.tencent.wcdb", "Yap Database": "dontknow"}, "Intelligent Platform": {"AWS IoT": "com.amazonaws.mobileconnectors.iot", "AWS Mobile Core": "com.amazonaws.mobile.client", "Bosmasmarthome Bosma": "com.bosma.smarthome", "IBM Watson": "com.ibm.watson.developer_cloud", "Imou Cloud Open Platform": "com.imou.cloud.open", "Kii Cloud": "com.kii.cloud.storage", "Neura": "com.neura.android.sdk", "Qnniu Health": "com.qn.health.sdk", "Sensoro": "com.sensoro.cloud.sdk", "Synerise": "com.synerise.sdk.android.synerise", "TUTK IOTC": "com.tutk.IOTC"}, "Network Connection": {"AFNetworking": "dontknow", "AFNetworking Activity Logger": "dontknow", "Adobe Experience Platform - Edge": "com.adobe.marketing.mobile.edge", "Agora Full Link Acceleration": "dontknow", "Alamofire": "dontknow", "Aliyun HTTPDNS": "com.alibaba.sdk.android.httpdns", "AndroidAsync": "com.koushikdutta.async", "CocoaAsyncSocket": "dontknow", "Google Volley": "com.android.volley", "HttpClient by Msebera": "com.loopj.android.http", "Keen IO": "io.keen.client.java", "LSDrupalSDK": "dontknow", "MQTT-Client-Framework": "dontknow", "MiniDNS": "org.minidns", "Moloco VAN": "dontknow", "Moya": "dontknow", "NetworkEye": "dontknow", "Qiniu Happy DNS": "com.qiniu.android.dns", "Reachability Swift": "dontknow", "RestKit": "dontknow", "Socket Rocket": "dontknow", "Socket.io": "io.socket", "Square OkHttp3": "com.squareup.okhttp3", "Square Retrofit": "retrofit2", "Square okio": "com.squareup.okio", "Starscream": "dontknow", "Tencent HttpDNS": "com.tencent.msdk.dns", "Tinder Scarlet": "com.tinder.scarlet", "XP2P": "dontknow", "YTK Network": "dontknow"}, "Performance Management": {"Adobe Experience Platform - Target": "com.adobe.marketing.mobile.target", "ACRA（Application Crash Reports for Android）": "ch.acra", "AWS Cloud Watch": "com.amazonaws.services.cloudwatch", "Adobe Experience Platform - Assurance": "com.adobe.marketing.mobile.assurance", "Airbrake": "io.airbrake.airbrake-java", "Alohalytics": "dontknow", "AndFix": "com.alipay.euler.andfix", "AppDynamics": "com.appdynamics.eum.android.agent", "Buddy Build": "com.buddy.sdk", "Bugfender": "com.bugfender.sdk", "Bugsee": "com.bugsee.android.sdk", "Bugsnag": "com.bugsnag.android", "Bugtags": "com.bugtags.library", "Cloudinary": "com.cloudinary.android", "CrashSight": "com.tencent.bugly.crashreport", "Crasheye": "com.crasheye.sdk.android", "Datadog": "com.datadog.android", "Deploygate": "com.deploygate.sdk", "Dynatrace OneAgent": "com.dynatrace.android.agent", "Ebay Spark Chamber": "dontknow", "Enhance": "com.fgl.enhance.android", "Firebase Crashlytics": "com.google.firebase.crashlytics", "Firebase Distribution": "com.google.firebase.appdistribution", "Firebase Performance": "com.google.firebase.perf", "GBDeviceInfo": "dontknow", "Google Toolbox": "com.google.toolbox.components", "Instabug": "com.instabug.library", "Iqiyi XCrash": "xcrash.lib.xcrash", "Iwangding Speedometer": "dontknow", "KSCrash": "dontknow", "Karte Crash Reporting": "io.karte.android.crashreporting", "Logentries logger": "com.logentries.logger", "Metrics Dropwizard": "io.dropwizard.metrics", "Netease Cloud Capture Bugrpt": "com.netease.nis.bugrpt", "New Relic": "com.newrelic.agent.android", "Oneapm": "com.oneapm.agent.android", "PLCrashReporter": "dontknow", "Panoramic surveillance (Woodpecker)": "dontknow", "Pendo": "io.pendo.android", "PgyUpdate": "com.pgyersdk.update", "Pgyer": "com.pgyersdk.feedback", "Quantcast Measure": "com.quantcast.measurement.service", "Raygun": "com.raygun.raygun4android", "Reedoun APP Monitoring": "dontknow", "Rollbar": "com.rollbar.android", "Sentry": "io.sentry.android.core", "Speedchecker": "com.speedchecker.android.sdk", "Split": "io.split.android.client", "Splunk MINT": "com.splunk.mint", "SpotBugs": "com.github.spotbugs", "Square Aardvark": "com.squareup.spoon", "Square LeakCanary": "com.squareup.leakcanary", "Square PonyDebugger": "com.squareup.ponydroid", "Tencent Bugly": "com.tencent.bugly", "Tencent Cloud Client Performance Analysis": "dontknow", "Tencent Tinker": "com.tencent.tinker.loader", "Teragence": "com.teragence.sdk", "UXCam": "com.uxcam", "Umeng Application Performance Monitoring": "com.umeng.analytics", "Volcengine Application Performance Monitoring": "dontknow"}, "State Synchronization": {"Iclouddocumentsync": "dontknow", "MMWormhole": "dontknow", "Simperium": "com.simperium.android", "Twilio State Synchronization": "com.twilio.sync", "Twilio Sync": "com.twilio.sync"}, "User Interface": {"ADTransitionController": "dontknow", "AHBottomNavigation": "com.aurelhubert.ahbottomnavigation", "AMTagListView": "dontknow", "Ace Drawing View": "dontknow", "Advanced RecyclerView": "com.h6ah4i.android.widget.advrecyclerview", "AdvancedWebView": "im.delight.android.webview", "Airbnb Lottie": "com.airbnb.android.lottie", "Android Auto Scroll ViewPager": "com.github.triniwiz.autoscrollviewpager", "Android Easing Functions": "com.daasuu.easing", "Android Image Slider": "com.daimajia.slider.library", "Android Material App Rating": "com.stepstone.apprating", "Android-Gif-Drawable": "pl.droidsonroids.gif", "Android-Iconics": "com.mikepenz.iconics", "Android-SpinKit": "com.github.ybq.android.spinkit", "AndroidSlidingUpPanel": "com.sothree.slidinguppanel", "AndroidSwipeLayout": "com.daimajia.swipelayout", "AppIntro": "com.github.appintro.AppIntro", "BLKFlexibleHeightBar": "dontknow", "Blurry": "jp.wasabeef.blurry", "BottomSheet": "com.flipboard.bottomsheet", "Calloutview": "dontknow", "CarbonKit": "dontknow", "Charts": "com.github.mikephil.charting", "Cicerone": "com.github.terrakok.cicerone", "CircleImageView": "de.hdodenhof.circleimageview", "CircleIndicator": "me.relex.circleindicator", "CircleProgress": "com.github.lzyzsd.circleprogress", "CircleView": "com.github.pavlospt.circleview", "CircularImageView": "com.mikhaellopez.circularimageview", "CircularProgressBar": "com.mikhaellopez.circularprogressbar", "Color Picker": "com.github.daniel-stoneuk.material-color-picker", "Cordova Plugin (In-App Browser)": "org.apache.cordova.inappbrowser", "Cordova Plugin (Splashscreen)": "org.apache.cordova.splashscreen", "DBGHTML Entities": "dontknow", "DTCoreText": "dontknow", "Dongtu Cloud Store SDK": "dontknow", "EAIntroView": "dontknow", "EMCCountryPickerController": "dontknow", "Ejecta": "dontknow", "Ezviz UIKit": "com.ezvizuikit", "FSCalendar": "dontknow", "Facebook Shimmer": "com.facebook.shimmer", "Fancybuttons": "com.github.medyo.fancybuttons", "Firebase UI": "com.firebaseui.auth", "Google BrailleBack": "com.google.android.marvin.brailleback", "Google Filament": "com.google.android.filament", "GraphView": "com.jjoe64.graphview", "HTAutocompleteTextField": "dontknow", "HelloCharts": "lecho.lib.hellocharts", "HoloEverywhere": "org.holoeverywhere", "IGListKit": "dontknow", "Icarousel": "dontknow", "Iflytek AIUI": "com.iflytek.aiui", "JGProgressHUD": "dontknow", "KVNProgress": "dontknow", "Koloda View": "dontknow", "LinkedIn Hakawai": "dontknow", "MKNumberBadgeView": "dontknow", "MPAndroidChart": "com.github.mikephil.charting", "Material": "com.github.material-components.material-components-android", "MaterialDateTimePicker": "com.wdullaer.materialdatetimepicker", "MaterialDrawer": "com.mikepenz.materialdrawer", "MaterialProgressBar": "me.zhanghai.android.materialprogressbar", "MaterialRangeBar": "com.appyvet.materialrangebar", "MaterialTextField": "com.github.florent37.materialtextfield", "NIDropDown": "dontknow", "NYTPhotoViewer": "dontknow", "NoNonsense-FilePicker": "com.nononsenseapps.filepicker", "NumberProgressBar": "com.daimajia.numberprogressbar", "PKHUD": "dontknow", "PhotoView": "com.github.chrisbanes.photoview", "React Native Vector Icons": "com.oblador.vectoricons", "React-Native-Spinkit": "com.react.spinkit", "RecyclerView Animators": "jp.wasabeef.recyclerview.animators", "RoundCornerProgressBar": "com.akexorcist.roundcornerprogressbar", "RoundedImageView": "com.makeramen.roundedimageview", "SDP": "com.intuit.sdp", "SHPKeyboardAwareness": "dontknow", "SVPulsingAnnotationView": "dontknow", "Sendbird UIKit": "com.sendbird.uikit", "ShortcutBadger": "me.leolin.shortcutbadger", "ShowcaseView": "com.github.amlcurran.showcaseview", "Skplanetx": "dontknow", "SmartRefreshLayout": "com.scwang.smartrefresh.layout", "SmartTabLayout": "com.ogaclejapan.smarttablayout", "SmoothProgressBar": "fr.castorflex.android.smoothprogressbar", "SnapKit": "dontknow", "Snapkit Masonry": "dontknow", "Stipop": "io.stipop.stipop-sdk-android", "Stream Activity Feed": "io.getstream.stream-chat-android", "TTTAttributedLabel": "dontknow", "TouchImageView": "com.ortiz.touch", "UITableView-FDTemplateLayoutCell": "dontknow", "UltimateRecyclerView": "com.marshalchen.ultimaterecyclerview", "ViewPagerTransforms": "com.ToxicBakery.viewpager.transforms", "ViewRevealAnimator": "com.github.florent37.viewreveal", "Weebly TableSchemer": "dontknow", "XL Form": "dontknow", "YLProgressBar": "dontknow", "ZDStickerView": "dontknow", "Zendesk Belvedere": "com.zendesk.belvedere"}}, "Security": {"Data Encryption": {"AESCrypt": "com.scottyab.aescrypt", "AWS KMS": "com.amazonaws.services.kms", "Cash App SQLDelight": "com.squareup.sqldelight", "JWT": "io.jsonwebtoken", "LUKeychainAccess": "dontknow", "LockSmith": "dontknow", "OpenPGP Api": "org.openintents.openpgp.api", "RNCryptor": "dontknow", "Square Valet": "dontknow", "Swift Keychain Wrapper": "dontknow"}, "Mobile Security": {"360 Security Protection": "com.qihoo.sdk.report", "Aliju Security": "dontknow", "Aliyun Cloud Device Risk Identification": "dontknow", "Aliyun Game Shield": "dontknow", "Anweishi Gameshield": "dontknow", "ApkProtector": "com.apkshell", "AppGuard": "com.appguard.sdk", "AppSuit Premium": "dontknow", "Baidu Application Hardening": "com.baidu.protect", "Bangcle Security Application Reinforcement": "com.bangcle.protect", "Bsfit Device Fingerprinting": "dontknow", "DexProtector": "com.licel.protection", "Dingxiang-Inc Application Hardening": "com.dingxiang.mobile", "Ijiami Secret Mobile Application Security Hardening": "com.ijiami.jiagu", "Jdcloud MCDN": "dontknow", "Kiwisec Secure Application Hardening": "com.kiwisec.sdk", "Msa-Alliance Security Alliance": "com.bun.miitmdid", "Netease Anti-cheat": "com.netease.htprotect", "Netease Application Hardening": "com.netease.mobsec", "Netease Handicraft Intelligent Anti-Hangout": "dontknow", "Netease SecurityGuard": "com.netease.mobsec.watch", "Shuzilm Alliance Trusted ID": "dontknow", "Tencent Cloud Mobile Game Security": "dontknow", "Tencent Legu": "com.tencent.bugly.legu", "Tongdun Device Profiling": "cn.fraudmetrix.android.sdk", "Tongfudun": "dontknow", "VirtualApp": "com.lody.virtual", "Volcengine Device Security": "dontknow"}, "Risk Control": {"Dingxiang-Inc Intelligent Wind Control": "dontknow", "Forter Trusted": "com.forter.mobile.sdk", "Ishumei Intelligent Business Risk Control": "com.ishumei.smantifraud", "Jdcloud Device Fingerprinting": "dontknow", "Tongdun Intelligent Risk Control": "cn.fraudmetrix.intellic"}, "Security Verification": {"1Password Extension": "com.agilebits.onepassword", "Bjca Authentication Cloud Signature Service": "dontknow", "BlackBerry Dynamics": "com.good.gd", "CargoBay": "dontknow", "Dingxiang-Inc Sensorless Authentication": "dontknow", "Esandinfo Trusted Authentication Service": "dontknow", "Firebase AppCheck": "com.google.firebase.appcheck", "Jdcloud Captcha": "com.jdcloud.captcha", "Netease Behavioral CAPTCHA": "com.netease.nis.captcha", "OneSpan Orchestration": "com.onespan.sdk.orchestration", "Sift": "com.sift.android.sdk", "Tongdun Smart Sensorless CAPTCHA": "dontknow", "TrueCaller": "com.truecaller.android.sdk"}}}}