#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK分析流程数据库测试脚本

根据 apk_analysis.md 指导，使用 config 里的配置进行本地测试，
从数据库中获取100条数据进行测试，并将发现的包插入数据库。
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
sys.path.insert(0, project_root)

# 导入配置和模型
from tenyy.config.local import DATABASE_CONFIG, DB_CONFIG
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from tenyy.src.models.app_version import AppVersion
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage

# 导入我们的SDK识别算法
from apk_analysis_flow import SDKIdentifier

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseAPKAnalyzer:
    """基于数据库的APK分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.sdk_identifier = SDKIdentifier()

        # 直接使用 config/local.py 的数据库配置
        db_url = f"postgresql://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

        self.engine = create_engine(db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.session = SessionLocal()

        logger.info(f"连接数据库: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}")
        
    def get_test_data(self, limit: int = 100) -> List[AppVersion]:
        """从数据库获取测试数据"""
        logger.info(f"从数据库获取{limit}条测试数据...")
        
        # 查询已完成分析且有包名数据的app_version记录
        app_versions = self.session.query(AppVersion).filter(
            AppVersion.analyze_status == 'completed',  # 使用 completed 状态
            AppVersion.packages_class.isnot(None),
            AppVersion.packages_class != '[]'  # 排除空数组
        ).limit(limit).all()
        
        logger.info(f"成功获取{len(app_versions)}条有效数据")
        return app_versions
    
    def extract_packages_from_db_record(self, app_version: AppVersion) -> List[str]:
        """从数据库记录中提取包名列表"""
        try:
            packages_class_value = getattr(app_version, 'packages_class', None)
            if not packages_class_value:
                return []

            # 解析JSON格式的包名列表
            if isinstance(packages_class_value, str):
                packages = json.loads(packages_class_value)
            else:
                packages = packages_class_value
            
            # 确保是列表格式
            if not isinstance(packages, list):
                logger.warning(f"app_version_id={app_version.id} 的packages_class不是列表格式")
                return []
            
            # 去重并排序
            unique_packages = sorted(list(set(packages)))
            logger.debug(f"app_version_id={app_version.id} 提取到{len(unique_packages)}个包名")
            
            return unique_packages
            
        except json.JSONDecodeError as e:
            logger.error(f"解析app_version_id={app_version.id}的packages_class失败: {e}")
            return []
        except Exception as e:
            logger.error(f"处理app_version_id={app_version.id}的数据时出错: {e}")
            return []
    
    def extract_main_package_name(self, app_version: AppVersion) -> Optional[str]:
        """从AndroidManifest中提取主包名"""
        try:
            android_manifest_value = getattr(app_version, 'android_manifest', None)
            if not android_manifest_value:
                return None

            import re
            # 使用正则表达式提取package属性
            match = re.search(r'package="([^"]+)"', android_manifest_value)
            if match:
                main_package = match.group(1)
                logger.debug(f"app_version_id={app_version.id} 主包名: {main_package}")
                return main_package

            return None

        except Exception as e:
            logger.error(f"提取app_version_id={app_version.id}的主包名失败: {e}")
            return None
    
    def analyze_single_app(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """分析单个应用的SDK"""
        logger.info(f"分析app_version_id={app_version.id}...")
        
        # 提取包名列表
        packages = self.extract_packages_from_db_record(app_version)
        if not packages:
            logger.warning(f"app_version_id={app_version.id} 没有有效的包名数据")
            return []
        
        # 提取主包名
        main_package = self.extract_main_package_name(app_version)
        
        # 使用SDK识别算法分析
        try:
            identified_sdks = self.sdk_identifier.identify_sdks(packages, main_package)
            logger.info(f"app_version_id={app_version.id} 识别出{len(identified_sdks)}个SDK")
            
            # 为每个SDK添加app_version_id
            for sdk in identified_sdks:
                sdk['app_version_id'] = app_version.id
            
            return identified_sdks
            
        except Exception as e:
            logger.error(f"分析app_version_id={app_version.id}时出错: {e}")
            return []
    
    def save_discovered_packages(self, sdks: List[Dict[str, Any]]) -> int:
        """将发现的包保存到数据库"""
        if not sdks:
            return 0
        
        saved_count = 0
        current_time = datetime.now()
        
        try:
            for sdk in sdks:
                # 检查是否已存在
                existing = self.session.query(class_AppDiscoveredPackage).filter(
                    class_AppDiscoveredPackage.app_version_id == sdk['app_version_id'],
                    class_AppDiscoveredPackage.package_name == sdk['package_name']
                ).first()
                
                if not existing:
                    # 创建新记录
                    discovered_package = class_AppDiscoveredPackage(
                        app_version_id=sdk['app_version_id'],
                        package_name=sdk['package_name'],
                        last_checked=current_time
                    )
                    self.session.add(discovered_package)
                    saved_count += 1
                else:
                    # 更新最后检查时间
                    setattr(existing, 'last_checked', current_time)
            
            # 提交事务
            self.session.commit()
            logger.info(f"成功保存{saved_count}个新发现的包到数据库")
            
        except Exception as e:
            logger.error(f"保存数据到数据库时出错: {e}")
            self.session.rollback()
            raise
        
        return saved_count
    
    def run_test(self, limit: int = 100):
        """运行完整的测试流程"""
        logger.info("=" * 60)
        logger.info("开始APK分析流程数据库测试")
        logger.info("=" * 60)
        
        try:
            # 1. 获取测试数据
            app_versions = self.get_test_data(limit)
            if not app_versions:
                logger.error("没有获取到测试数据")
                return
            
            # 2. 分析每个应用
            all_discovered_sdks = []
            processed_count = 0
            
            for app_version in app_versions:
                try:
                    sdks = self.analyze_single_app(app_version)
                    all_discovered_sdks.extend(sdks)
                    processed_count += 1
                    
                    # 显示进度
                    if processed_count % 10 == 0:
                        logger.info(f"处理进度: {processed_count}/{len(app_versions)} ({processed_count/len(app_versions)*100:.1f}%)")
                
                except Exception as e:
                    logger.error(f"处理app_version_id={app_version.id}时出错: {e}")
                    continue
            
            # 3. 保存结果到数据库
            logger.info(f"总共发现{len(all_discovered_sdks)}个SDK包")
            saved_count = self.save_discovered_packages(all_discovered_sdks)
            
            # 4. 输出统计信息
            logger.info("=" * 60)
            logger.info("测试完成统计:")
            logger.info(f"  处理应用数量: {processed_count}")
            logger.info(f"  发现SDK总数: {len(all_discovered_sdks)}")
            logger.info(f"  新保存包数量: {saved_count}")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"测试过程中出错: {e}")
            raise
        finally:
            # 关闭数据库连接
            self.session.close()

def main():
    """主函数"""
    try:
        analyzer = DatabaseAPKAnalyzer()
        analyzer.run_test(limit=100)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
