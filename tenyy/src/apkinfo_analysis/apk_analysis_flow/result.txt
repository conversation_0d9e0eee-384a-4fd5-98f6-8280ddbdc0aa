{"status": "success", "processed_files": 3, "timestamp": "2025-08-07T07:10:28.528824", "results": [{"file": "class_example.txt", "total_packages": 9146, "identified_sdks": 46, "sdks": ["com.tencent", "com.tencent.thumbplayer", "com.google.android", "com.google", "com.tencent.pigeon", "com.tencent.mapsdk", "com.tencent.luggage", "com.tencent.liteav", "com.tencent.matrix", "com.tencent.tencentmap", "io.flutter", "com.tencent.midas", "com.tencent.map", "com.tencent.tencentmap.mapsdk", "com.tencent.qqmusic", "com.tencent.maas", "com.tencent.qqmusic.mediaplayer", "com.tencent.tmassistantsdk", "com.tencent.wechat", "com.tencent.mars", "com.tencent.tav", "com.tencent.youtu", "com.tencent.xweb", "org.chromium", "com.tenpay", "com.google.firebase", "com.tencent.kinda.framework", "com.tencent.kinda", "io.flutter.embedding.engine", "com.tencent.liteapp", "io.flutter.plugins", "com.tencent.tinker", "io.flutter.embedding", "org.chromium.base", "com.tencent.magicbrush", "com.tencent.tavkit", "io.flutter.embedding.engine.plugins", "io.clipworks", "io.flutter.plugin", "com.dave<PERSON>rissey", "com.hihonor", "com.eclipsesource", "com.huawei", "com.github", "com.facebook", "com.tencent.wevision2"]}, {"file": "class_example2.txt", "total_packages": 8496, "identified_sdks": 147, "sdks": ["com.tencent", "com.tencent.qimei", "com.tencent.trpcprotocol", "com.tencent.ams.fusion.widget", "com.tencent.qqlive.module.videoreport", "com.tencent.common", "com.xueersi.common", "com.tencent.qqlive.tvkplayer", "com.facebook", "com.tencent.ams", "com.tencent.kuikly.core", "com.tencent.ad.tangram", "com.tencent.thumbplayer", "com.tencent.rmonitor", "com.tencent.qqlive", "com.xueersi", "com.xueersi.lib", "com.xueersi.meta.modules.plugin", "com.tencent.ams.fusion", "com.tencent.thumbplayer.core", "cn.hutool.core", "com.tencent.beacon", "com.huawei", "com.tencent.qqlive.modules", "com.google", "com.tencent.qqlive.module", "com.google.android.material", "com.tdsrightly", "com.xueersi.meta.modules", "com.xueersi.meta", "com.tencent.mobileqq", "com.tdsrightly.qmethod", "com.tencent.tinker", "com.xueersi.parentsmeeting", "com.tdsrightly.qmethod.monitor", "com.tencent.qqmini", "com.tencent.opentelemetry", "com.facebook.imagepipeline", "cn.hut<PERSON>", "com.meizu", "com.meizu.cloud", "com.tencent.mobileqq.triton", "io.flutter", "com.tencent.midas", "com.xueersi.meta.base", "com.tencent.bugly", "com.xueersi.meta.base.live.framework", "com.facebook.drawee", "tencent.doc", "com.iflytek", "com.tencent.mttreader", "com.bumptech", "com.tencent.gathererga", "com.hihonor", "org.apache", "com.alibaba", "com.meizu.cloud.pushsdk", "com.tencent.bugly.common", "com.xes", "org.tensorflow", "com.tencent.rdelivery.reshub", "com.xes.meta", "com.bumptech.glide", "tencent.doc.opensdk", "com.tencent.rdelivery", "com.jayway", "com.tencent.rfix.lib", "com.tencent.superplayer", "com.tencent.raft", "tv.danmaku", "okhttp3.internal", "com.tencent.rfix.loader", "com.tencent.rfix", "com.facebook.common", "tencent.doc.opensdk.openapi", "qb.foundation", "com.tencent.startrail.report", "com.google.android", "com.honor", "com.tencent.galileo", "org.jraf", "com.tencent.paysdk", "com.tencent.upgrade", "com.facebook.fresco", "com.tencent.startrail", "com.tencent.portraitsdk", "com.iflytek.cloud", "com.vivo.push", "com.huawei.secure.android.common", "com.tencent.tuxmetersdk", "com.tencent.raft.raftframework", "com.tencent.matrix", "com.tencent.apkchannel", "com.tencent.open", "org.tensorflow.lite", "com.tencent.qbmnnbusiness", "org.xutils.xutils", "com.airbnb.lottie", "com.xiaomi", "io.flutter.embedding.engine", "com.tencent.startrail.report.vendor", "com.heytap", "com.ZWApp.Api", "com.tencent.odk.player.client", "com.hihonor.push", "com.tencent.connect", "io.flutter.embedding", "com.tencent.tar", "com.tencent.qcloud.core", "com.fluttercandies", "com.tencent.qb", "org.xutils", "com.heytap.mcssdk", "com.huya", "com.facebook.fresco.animation", "com.huawei.agconnect", "com.xueersi.ui.smartrefresh", "org.tensorflow.lite.support", "net.lingala", "com.tencent.shadow", "com.google.gson", "com.tencent.tddiag", "com.vivo", "com.airbnb", "com.tencent.qcloud", "com.jayway.jsonpath", "com.tencent.qqvideo.edgeengine", "com.google.archivepatcher", "com.tencent.tmediacodec", "com.xueersi.component.cloud", "com.fluttercandies.flutter_image_compress", "io.flutter.embedding.engine.plugins", "com.ZWApp", "com.localsearch", "tmsdk.common.gourd", "com.tencent.trouter", "com.oa", "tmsdk.common", "com.jayway.jsonpath.internal", "com.tencent.memorycanary", "com.tencent.qbmnn", "net.minidev", "com.pay", "com.tencent.nativevue.hippy", "org.mozilla", "com.huya.huyasdk", "com.tencent.rmpbusiness"]}, {"file": "class_example3.txt", "total_packages": 1882, "identified_sdks": 100, "sdks": ["cn.jiguang", "com.beizi.fusion", "com.huawei", "com.beizi", "com.ibplus.client", "com.facebook", "com.tencent", "com.google.firebase", "com.google", "com.umeng", "org.apache", "com.google.android.material", "com.google.android", "kt.pieceui", "kt.pieceui.activity", "com.taobao", "com.huawei.updatesdk", "com.google.android.exoplayer2", "com.alibaba", "com.ibplus", "com.google.zxing", "com.meizu", "com.meizu.cloud", "com.taobao.aranger", "com.huawei.hianalytics", "org.parceler", "com.youzan", "com.luck.picture.lib", "anet.channel", "cn.jpush.android", "com.facebook.drawee", "kt.pieceui.fragment", "com.umeng.socialize", "com.bumptech", "com.alipay", "com.iflytek", "com.example", "com.huawei.appmarket.component.buoycircle", "com.luck", "com.meizu.cloud.pushsdk", "cn.sharesdk", "com.github", "com.lljjcoder", "com.chad", "com.bumptech.glide", "tv.danmaku", "com.taobao.accs", "in.srain", "com.huawei.appmarket.component.buoycircle.impl", "okhttp3.internal", "kt.widget", "me.everything", "com.umeng.message", "com.umeng.commonsdk", "com.beizi.ad.internal", "com.mob", "com.alibaba.fastjson", "com.example.libimagefilter", "cn.j<PERSON>h", "com.liulishuo.filedownloader", "com.xiaomi", "com.tencent.open", "com.youzan.androidsdk", "com.facebook.imagepipeline", "com.youzan.spiderman", "com.iflytek.cloud", "com.alibaba.fastjson.support", "com.vivo.push", "kt.bean", "org.jetbrains.anko", "anetwork.channel", "com.scwang.smartrefresh.layout", "com.google.gson", "com.kit.jdkit_library.jdwidget", "com.youzan.jsbridge", "com.kit", "com.vivo", "org.jetbrains", "org.kymjs.kjframe", "org.parceler.guava", "cn.sharesdk.framework", "org.android", "com.chad.library.adapter.base", "com.scwang", "com.daimajia", "com.mob.tools", "tv.danmaku.ijk.media", "jp.was<PERSON><PERSON>", "jp.co", "org.greenrobot", "org.greenrobot.greendao", "com.tencent.connect", "com.liulishuo", "com.volokh", "kt.base", "kt.pieceui.adapter", "com.lljjcoder.citypickerview", "kt.search", "org.android.agoo", "com.karics"]}]}