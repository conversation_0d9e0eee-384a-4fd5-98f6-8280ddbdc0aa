# APK SDK 自动化分析与识别方案（v1.0）

本方案旨在构建一个高效、可扩展且具备自学习能力的自动化SDK识别系统。核心思想是将 **数据采集** 与 **数据匹配** 彻底解耦，通过引入专门的数据表和优化的工作流，实现极致的匹配效率和天然的回溯能力。

---

## 核心架构

系统由两个独立的 Prefect 流程和一个核心知识库构成：

1.  **[apk_analysis_flow](file:///home/<USER>/dev/tenyy-dind/tenyy/src/apkinfo_analysis/flows.py#L239-L342) (APK分析与包名提纯流程)**：从数据库中已存储的APK分析数据中提取并通过智能过滤规则提纯出高质量的"候选包名"，存入 [class_app_discovered_packages](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_discovered_packages.py#L0-L0) 表。
2.  **`sdk_matching_flow` (SDK匹配与关联流程)**：核心匹配引擎。它不直接处理APK，而是高效地在数据库层面完成 [class_app_discovered_packages](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_discovered_packages.py#L0-L0) 与 [class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0) 的匹配，并更新最终结果。
3.  **`knowledge_base_enrichment_flow` (知识库增强流程)**：后台的自学习引擎。它利用大语言模型（LLM）分析未知的包名，持续扩充 [class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)。

这种设计确保了新知识能自动应用于所有历史数据，实现了"一次学习，处处生效"。


---

## 第一阶段：APK分析与包名提纯 ([apk_analysis_flow](file:///home/<USER>/dev/tenyy-dind/tenyy/src/apkinfo_analysis/flows.py#L239-L342))

此流程是数据采集的入口，其目标是从数据库中已存储的APK分析数据中提取出最有可能代表第三方SDK的"候选包名"。

**重要说明**：此流程不涉及APK文件的下载和解压，而是直接从数据库中的 [app_version](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/app_version.py#L0-L0) 表获取已分析的APK数据。

### 1.1 数据源获取

从 [app_version](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/app_version.py#L0-L0) 表中获取已分析的APK数据：
-   读取 [android_manifest](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/app_version.py#L87-L87) 字段：获取应用的主包名（`package`）和权限等信息
-   读取 [packages_class](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/app_version.py#L88-L88) 字段：获取所有已提取的Java包名列表
-   确保 [analyze_status](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/app_version.py#L83-L83) 为 `done`，表示APK已完成分析

### 1.2 数据预处理

对从数据库获取的包名数据进行预处理：
-   验证 [packages_class](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/app_version.py#L88-L88) 字段的数据完整性
-   解析JSON格式的包名列表
-   去除重复的包名
-   按字典序排序包名列表

### 1.3 智能过滤与提纯

为了进一步提纯数据，必须对从数据库获取的包名进行严格的\"智能过滤\"：

### 1.3.1 基于\"局部最优分数\"的SDK识别算法

**核心改进**：
1. **Trie树构建优化**：
- 在构建Trie树时，同时更新每个节点的计数，表示该节点代表的包名前缀出现的总次数
- 通过遍历所有包名并逐级构建树形结构，确保每个节点都包含其在层级结构中的统计信息
- 优化后的Trie树构建算法：
```python
# 优化的Trie树构建算法
def _build_trie(self, packages_class):
    root = TrieNode("")
    for package_name in packages_class:
        # 统一包名格式（处理斜杠分隔）
        package_name = package_name.replace('/', '.')
        parts = package_name.split('.')
        
        current_node = root
        prefix_parts = []
        
        # 逐级构建Trie树
        for part in parts:
            prefix_parts.append(part)
            current_prefix = '.'.join(prefix_parts)
            
            # 更新当前节点计数
            current_node.count += 1
            
            # 创建或移动到子节点
            if part not in current_node.children:
                current_node.children[part] = TrieNode(current_prefix)
            current_node = current_node.children[part]
        
        # 更新叶子节点计数
        current_node.count += 1
    return root
```

2. **断点分数计算优化**：
- 引入字符分布熵值和数字占比等新特征
- 增加详细的调试日志输出
```python
def _compute_breakpoint_score(self, node):
    """计算节点的断点分数，新增详细的调试日志"""
    if node.is_breakpoint_score_computed:
        return node.breakpoint_score
    
    # 如果没有子节点，断点分数为0
    if not node.children:
        node.breakpoint_score = 0.0
        node.is_breakpoint_score_computed = True
        return 0.0
    
    # 找到密度最高的子节点
    max_child_density = max(child.count for child in node.children.values())
    
    # 计算断点分数
    if node.count == 0:
        node.breakpoint_score = 0.0
    else:
        ratio = max_child_density / node.count
        node.breakpoint_score = node.count * (1 - ratio)
    
    node.is_breakpoint_score_computed = True
    logging.debug(f'计算断点分数: {node.prefix} | 分数={node.breakpoint_score:.2f} | '
                f'节点计数={node.count} | 最大子节点密度={max_child_density}')
    return node.breakpoint_score
```

3. **局部最优点识别优化**：
- 增加路径分析和统计信息输出
```python
def _find_local_optima(self, root, full_package_name):
    """在完整包名路径上找到局部最优点，新增详细的路径分析"""
    # 处理斜杠分隔的包名
    full_package_name = full_package_name.replace('/', '.')
    parts = full_package_name.split('.')
    
    current_node = root
    path_nodes = [root]  # 包含路径上的所有节点
    
    # 构建路径
    prefix_parts = []
    for part in parts:
        if part in current_node.children:
            current_node = current_node.children[part]
            path_nodes.append(current_node)
        else:
            break
    
    # 计算路径上所有节点的断点分数
    max_score = -1
    best_node = None
    
    for node in path_nodes:
        score = self._compute_breakpoint_score(node)
        if score > max_score:
            max_score = score
            best_node = node
    
    # 输出路径分析结果
    logging.debug(f'路径分析: {full_package_name} | 路径长度={len(path_nodes)} | 最高分数={max_score:.2f} | '
                f'最佳节点={best_node.prefix if best_node else "None"}')
    
    return best_node.prefix if best_node else None
```

### 1.3.2 过滤掉明显不是SDK的包

**增强的过滤规则**：
1. **系统和标准库包过滤**：
- 新增的已知缩写白名单机制，允许保留常用缩写
- 优化的混淆包识别逻辑：
```python
# 新增的字符分布熵值计算
def _calculate_entropy(self, s):
    from collections import defaultdict
    if not s:
        return 0
    # 计算每个字符的频率
    freq = defaultdict(int)
    for c in s:
        freq[c] += 1
    # 计算熵值
    entropy = 0
    total = len(s)
    for count in freq.values():
        p = count / total if total > 0 else 0
        if p > 0:
            entropy -= p * log2(p)
    return entropy
```

2. **应用自身包过滤**：
- 增强的主包名提取逻辑：
```python
# 改进的主包名提取方法
def extract_main_package_name(self, android_manifest):
    import re
    if android_manifest and 'package=' in android_manifest:
        # 使用正则表达式提取package属性
        match = re.search(r'package="([^"]+)"', android_manifest)
        if match:
            return match.group(1)
    return None
```

3. **混淆包过滤**：
- 增加的数字占比计算：
```python
# 新增的数字占比计算
def _calculate_digit_ratio(self, s):
    if not s:
        return 0
    digit_count = sum(c.isdigit() for c in s)
    return digit_count / len(s) if len(s) > 0 else 0
```

### 1.4 数据存储

**增强的存储机制**：
1. 增加的批量处理统计信息：
```python
# 批量插入时输出处理统计信息
print(f'成功插入 {len(discovered_packages)} 条记录...')
```
2. 添加的数据验证检查点：
```python
# 插入前验证数据有效性
assert package_name, '包名不能为空'
assert app_version_id > 0, '无效的app_version_id'
```

## 第四阶段：apk_analysis_flow程序实现

**关键改进**：
1. 添加的详细的调试日志输出：
```python
# 在初始化时配置日志
def _configure_logging(self):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    # 降低第三方库日志级别
    logging.getLogger('sqlalchemy').setLevel(logging.WARNING)
```
2. 增加的过滤统计信息：
```python
# 在过滤后输出统计信息
print(f'过滤后剩余 {len(filtered_packages)} 个包名')
print(f'可疑包过滤数量：{suspicious_count}')
print(f'黑名单过滤数量：{blacklist_count}')
```
3. 优化的异常处理机制：
```python
# 改进的异常处理
def process_real_data(self):
    try:
        # ... 数据处理 ...
    except json.JSONDecodeError as e:
        logging.error(f'解析数据失败: {e}')
        session.rollback()
    except Exception as e:
        logging.error(f'处理数据时出错: {e}')
        session.rollback()
```

## 总结

**新增的验证与优化**：
- 在测试数据处理中添加数据格式验证：
```python
# 测试数据处理的验证
assert len(packages_class) > 0, '测试数据为空'
for package in filtered_packages:
    assert '.' in package, '包名格式不正确'
```
- 在真实数据处理中添加性能监控：
```python
# 添加处理时间监控
import time
start_time = time.time()
# ... 数据处理 ...
total_time = time.time() - start_time
print(f'数据处理耗时: {total_time:.2f} 秒')
```
- 在运行时添加进度跟踪：
```python
# 添加处理进度跟踪
processed = 0
for app_version in app_versions:
    # ... 处理 ...
    processed += 1
    print(f'处理进度: {processed}/{len(app_versions)} ({processed/len(app_versions)*100:.1f}%)')
```

