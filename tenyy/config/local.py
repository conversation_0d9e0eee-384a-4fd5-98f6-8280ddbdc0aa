# -*- coding: utf-8 -*-
"""
Tenyy 统一配置系统 - 本地开发环境配置

本地开发环境特定的配置，使用localhost地址和本地端口。
适用于直接在开发机上运行Python程序的场景。
"""

import os
from .base import *  # 导入所有基础配置

# ============================================================================
# 🌍 环境标识
# ============================================================================

ENVIRONMENT = "local"
ENVIRONMENT_NAME = "本地开发环境"

# ============================================================================
# 🗄️ 数据库配置 - 本地开发
# ============================================================================

DATABASE_CONFIG = {
    "host": os.getenv("POSTGRES_HOST", "localhost"),
    "port": int(os.getenv("POSTGRES_PORT", "5432")),  # 本地开发端口
    "database": os.getenv("POSTGRES_DB", "tenyy_app"),
    "username": os.getenv("POSTGRES_USER", "admin"),
    "password": os.getenv("POSTGRES_PASSWORD", "zhangdi168"),
}

# 统一数据库环境变量配置 (仅使用POSTGRES_*格式)
DB_CONFIG = {
    "POSTGRES_HOST": DATABASE_CONFIG["host"],
    "POSTGRES_PORT": str(DATABASE_CONFIG["port"]),
    "POSTGRES_DB": DATABASE_CONFIG["database"],
    "POSTGRES_USER": DATABASE_CONFIG["username"],
    "POSTGRES_PASSWORD": DATABASE_CONFIG["password"],
}

# ============================================================================
# 🔄 Prefect配置 - 本地开发
# ============================================================================

PREFECT_CONFIG = {
    "api_url": os.getenv("PREFECT_API_URL", "http://localhost:4200/api"),
    "ui_url": "http://localhost:4200",
    "work_pool_name": WORK_POOL_NAME,
    "logging_level": "INFO",
}

# ============================================================================
# 📥 Aria2配置 - 本地开发
# ============================================================================

ARIA2_CONFIG = {
    "rpc_url": os.getenv("ARIA2_RPC_URL", "http://localhost:6800/jsonrpc"),
    "rpc_token": os.getenv("ARIA2_RPC_TOKEN", "zhangdi168"),
    "download_dir": os.getenv("DOWNLOAD_DIR", "/downloads"),  # Aria2容器内下载目录
    **ARIA2_COMMON_CONFIG  # 继承通用配置
}

# ============================================================================
# 🐳 Docker配置 - 本地开发
# ============================================================================

DOCKER_REGISTRY = {
    "host": "localhost",
    "port": "5000",
    "url": "localhost:5000",
}

DOCKER_IMAGE_CONFIG = {
    "registry": DOCKER_REGISTRY["url"],
    "image_name": DOCKER_CONFIG["image_name"],
    "tag": DOCKER_CONFIG["image_tag"],
    "full_image": f"{DOCKER_REGISTRY['url']}/{DOCKER_CONFIG['image_name']}:{DOCKER_CONFIG['image_tag']}",
}

# ============================================================================
# 🌐 网络配置 - 本地开发
# ============================================================================

NETWORK_CONFIG = {
    "docker_network": "test-stack_tenyy-net",
    "network_mode": "host",  # 本地开发使用host网络
}

# ============================================================================
# 📁 存储路径配置 - 本地开发
# ============================================================================

LOCAL_STORAGE_CONFIG = {
    **STORAGE_CONFIG,  # 继承基础存储配置
    "host_download_dir": "/mnt/ssd/tenyy/downloads",  # 本地开发环境下载目录
}

# ============================================================================
# 🔧 Prefect部署配置 - 本地开发
# ============================================================================

PREFECT_DEPLOYMENT_CONFIG = {
    "work_pool": {
        "name": WORK_POOL_NAME,
        "type": "docker",
    },
    "job_variables": {
        "image": DOCKER_IMAGE_CONFIG["full_image"],
        "network_mode": NETWORK_CONFIG["network_mode"],
        "env": {
            "PREFECT_LOGGING_LEVEL": "INFO",
            "PYTHONPATH": DOCKER_CONFIG["pythonpath"],
            **DB_CONFIG,  # 数据库环境变量
            **{
                "ARIA2_RPC_URL": ARIA2_CONFIG["rpc_url"],
                "ARIA2_RPC_TOKEN": ARIA2_CONFIG["rpc_token"],
                "DOWNLOAD_DIR": ARIA2_CONFIG["download_dir"],
            }
        },
        "volumes": [
            f"{LOCAL_STORAGE_CONFIG['host_download_dir']}:{ARIA2_CONFIG['download_dir']}",
            f"/tmp/apk_downloads:{LOCAL_STORAGE_CONFIG['temp_apk_dir']}",
        ],
        **RESOURCE_LIMITS["crawler"],  # 使用爬虫资源限制作为默认
    }
}

# ============================================================================
# 🕷️ 爬虫特定配置 - 本地开发
# ============================================================================

CRAWLER_CONFIG = {
    "config_dir": "tenyy/src/crawler/configs",
    "yinyongbao_config": "tenyy/src/crawler/configs/yinyongbao.yaml",
    "huawei_config": "tenyy/src/crawler/configs/huawei.yaml",
    "categories_dir": "tenyy/src/crawler",
}

# ============================================================================
# 📦 Download-Extract特定配置 - 本地开发
# ============================================================================

DOWNLOAD_EXTRACT_CONFIG = {
    "scheduler_deployment": {
        "name": "adaptive-scheduler-local",
        "description": "自适应调度器 - 本地开发版",
        "work_queue": "scheduler-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["scheduler"],
        }
    },
    "processor_deployment": {
        "name": "apk-processor-local", 
        "description": "APK处理器 - 本地开发版",
        "work_queue": "processor-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["processor"],
        }
    }
}

# ============================================================================
# 🔧 环境验证函数
# ============================================================================

def validate_local_config() -> bool:
    """验证本地环境配置"""
    required_configs = [
        DATABASE_CONFIG,
        PREFECT_CONFIG,
        ARIA2_CONFIG,
        DOCKER_REGISTRY,
        NETWORK_CONFIG,
    ]
    
    # 检查必要的配置项
    for config in required_configs:
        if not config:
            return False
    
    # 检查端口配置
    if DATABASE_CONFIG["port"] != 5432:
        print("警告: 本地数据库端口不是5432可能与其他环境冲突")
    
    return True

# 在模块加载时验证配置
if not validate_local_config():
    raise ValueError("本地环境配置验证失败，请检查配置")

# ============================================================================
# 🚀 Prefect部署配置 - 本地环境
# ============================================================================

# 本地环境Prefect部署配置
HOST_DOWNLOAD_DIR = "/mnt/ssd/tenyy/downloads"
DOCKER_IMAGE = "localhost:5000/tenyy-unified:latest"
DOCKER_NETWORK = "test-stack_tenyy-net"
PREFECT_API_URL = "http://localhost:4200/api"

# ============================================================================
# 🔧 配置导出
# ============================================================================

# 导出给其他模块使用的配置字典
CONFIG = {
    "environment": ENVIRONMENT,
    "database": DATABASE_CONFIG,
    "prefect": PREFECT_CONFIG,
    "aria2": ARIA2_CONFIG,
    "docker": DOCKER_IMAGE_CONFIG,
    "network": NETWORK_CONFIG,
    "storage": LOCAL_STORAGE_CONFIG,
    "crawler": CRAWLER_CONFIG,
    "download_extract": DOWNLOAD_EXTRACT_CONFIG,
}
