# Docker Swarm 生产环境配置
#
# 重要改动 (2025-07-19):
# - 统一工作池名称为 tenyy-unified-pool
# - 增强数据持久化安全，确保重新部署不丢失数据
# - 添加文件共享卷支持 APK 下载和分析
# - 优化资源配置支持 APK 分析任务
#
# 重要改动 (2025-07-11):
# - 修改 Prefect 服务使用内部服务名通信 (prefect-server:4200)，避免外部IP连接问题
# - 改进 Worker 重启策略和健康检查，提高稳定性
# - 解决 Worker 容器频繁 shutdown 的网络连接问题
#
version: '3.8'

# 全局日志配置 - 应用于所有服务
x-logging: &default-logging
  driver: json-file
  options:
    max-size: "10m"
    max-file: "3"

services:
  # PostgreSQL 数据库
  app-db:
    image: postgres:15
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD:-your_strong_password_here}
      POSTGRES_DB: tenyy_app
    ports:
      - "5432:5432"  # 临时暴露端口，方便本地连接
    volumes:
      - tenyy_db_data:/var/lib/postgresql/data
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == worker
      resources:
        limits:
          memory: 8G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      # 确保数据库数据持久化安全
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        order: stop-first  # 先停止旧容器再启动新容器，保护数据
      rollback_config:
        parallelism: 1
        delay: 30s
        order: stop-first
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d tenyy_app"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging: *default-logging  # 应用全局日志配置

  # Prefect 数据库
  prefect-db:
    image: postgres:15
    environment:
      POSTGRES_USER: prefect
      POSTGRES_PASSWORD: ${PREFECT_DB_PASSWORD:-another_strong_password}
      POSTGRES_DB: prefect_server
    volumes:
      # 使用更直接、更可靠的"直接绑定挂载"语法
      - /mnt/ssd/tenyy/prefect_db:/var/lib/postgresql/data
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 30s
        max_attempts: 5
        window: 120s
      resources:
        limits:
          memory: 5G
          cpus: '2'
        reservations:
          memory: 512M
          cpus: '0.25'
      # 确保Prefect数据库数据持久化安全
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        order: stop-first
      rollback_config:
        parallelism: 1
        delay: 30s
        order: stop-first
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U prefect -d prefect_server"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging: *default-logging  # 应用全局日志配置

  # Prefect Server
  prefect-server:
    image: prefecthq/prefect:3.4.8-python3.11
    command: prefect server start
    environment:
      PREFECT_API_DATABASE_CONNECTION_URL: "postgresql+asyncpg://prefect:${PREFECT_DB_PASSWORD:-another_strong_password}@prefect-db:5432/prefect_server"
      PREFECT_SERVER_API_HOST: "0.0.0.0"
      PREFECT_ANALYTICS_ENABLED: "false"
      PREFECT_API_URL: "http://*************:4200/api"
      PREFECT_UI_API_URL: "http://*************:4200/api"
      # --- 新的数据保留策略 ---
      # 保留最近5天的日志
      PREFECT_SERVER_LOG_RETENTION_DAYS: 5
      
      # 保留最近5天的流程和任务运行历史
      # 如果不设置，运行历史将永久保留
      PREFECT_SERVER_RUN_RETENTION_DAYS: 5
    volumes:
      - prefect_server_data:/root/.prefect
    ports:
      - "4200:4200"
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    depends_on:
      - prefect-db
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import urllib.request; exit(0) if urllib.request.urlopen('http://localhost:4200/api/health').getcode() == 200 else exit(1)\" || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging: *default-logging  # 应用全局日志配置

  # 私有Docker Registry
  docker-registry:
    image: registry:2
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == worker
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'
    # 'environment' 块与 'deploy' 'image' 等平级
    environment:
      REGISTRY_STORAGE_DELETE_ENABLED: "true"
      REGISTRY_HTTP_ADDR: "0.0.0.0:5000"
    logging: *default-logging  # 应用全局日志配置

  # Prefect Worker for Docker Swarm - 统一工作池
  prefect-worker:
    image: prefecthq/prefect:3.4.8-python3.11
    command: sh -c "pip install prefect-docker && prefect worker start -p tenyy-unified-pool -t docker"
    environment:
      PREFECT_API_URL: "http://prefect-server:4200/api"
      PREFECT_LOGGING_LEVEL: INFO
      POSTGRES_HOST: 'app-db'
      POSTGRES_PORT: '5432'
      POSTGRES_USER: 'admin'
      POSTGRES_PASSWORD: '${DB_PASSWORD:-your_strong_password_here}'
      POSTGRES_DB: 'tenyy_app'
      DB_HOST: 'app-db'
      DB_PORT: '5432'
      DB_USER: 'admin'
      DB_PASSWORD: '${DB_PASSWORD:-your_strong_password_here}'
      DB_NAME: 'tenyy_app'
      ARIA2_RPC_URL: "http://aria2-service:6800/jsonrpc"
      ARIA2_RPC_TOKEN: "${ARIA2_RPC_TOKEN:-zhangdi168}"
      DOWNLOAD_DIR: "/downloads"
      TENYY_ENV: "production"
      DOCKER_ENV: "true"
      PYTHONPATH: "/app"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - aria2_downloads:/downloads
      - apk_temp_storage:/tmp/apk_downloads
    networks:
      - tenyy-net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == worker
      # 'resources' 块需要与 'placement' 和 'restart_policy' 保持同级缩进
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
      restart_policy:
        condition: any
        delay: 15s
        max_attempts: 10
    depends_on:
      - prefect-server
      - prefect-db
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import urllib.request; exit(0) if urllib.request.urlopen('http://prefect-server:4200/api/health').getcode() == 200 else exit(1)\" || exit 1"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 120s
    logging: *default-logging  # 应用全局日志配置

volumes:
  # 数据库数据卷
  tenyy_db_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/db
  
  # prefect_db_data 的定义已被移除
  
  prefect_server_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/prefect_server

  # Registry数据卷
  registry_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/registry

  # Aria2相关数据卷 (保留卷定义以便其他服务访问)
  aria2_downloads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/downloads
  aria2_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/aria2_config

  # APK临时存储卷
  apk_temp_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/ssd/tenyy/apk_temp

networks:
  tenyy-net:
    driver: overlay
    attachable: true

# ============================================================================
# 🚀 部署说明和数据安全配置
# ============================================================================

# 部署前准备工作:
# 1. 在所有worker节点上创建数据目录(使用SSD主磁盘):
#    sudo mkdir -p /mnt/ssd/tenyy/{db,prefect_db,prefect_server,registry,downloads,aria2_config,apk_temp}
#    sudo chown -R 1000:1000 /mnt/ssd/tenyy
#    sudo chmod -R 755 /mnt/ssd/tenyy
#
# 2. 设置环境变量
#    export DB_PASSWORD=your_strong_password_here
#    export PREFECT_DB_PASSWORD=another_strong_password
#    export ARIA2_RPC_TOKEN=zhangdi168
#
# 3. 部署命令:
#    docker stack deploy -c docker-swarm-compose.yml tenyy-crawler-stack
#
# 4. 验证部署:
#    docker service ls
#    docker service logs tenyy-crawler-stack_prefect-worker
#
# 数据安全保证:
# - 所有数据卷使用绑定挂载到 /data/tenyy 目录
# - 数据库服务使用 stop-first 更新策略，确保数据完整性
# - 重新部署不会影响现有数据，数据持久化在宿主机文件系统
# - 支持数据备份: tar -czf tenyy-backup-$(date +%Y%m%d).tar.gz /data/tenyy

# Swarm 高级配置 (可选)
# configs:
#   crawler_config:
#     external: true

# secrets:
#   db_password:
#     external: true
#   prefect_db_password:
#     external: true

# ============================================================================
# 🔧 服务访问端点
# ============================================================================
# - Prefect UI: http://*************:4200
# - Docker Registry: http://*************:5000
# - PostgreSQL (App): *************:5432
# ============================================================================