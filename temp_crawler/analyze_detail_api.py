#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析SDK详情页面的API
"""

import requests
import json
import time

def analyze_detail_api():
    """分析SDK详情页面的API调用"""
    
    session = requests.Session()
    
    # 建立session
    main_url = "https://sdk.caict.ac.cn/official/"
    session.get(main_url, timeout=10)
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Sec-<PERSON>tch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    session.headers.update(headers)
    
    # 测试recordId
    test_record_id = "d865d6ed558b4214bdd26cea9116b508"
    
    print(f"测试recordId: {test_record_id}")
    print("=" * 60)
    
    # 可能的API端点
    possible_endpoints = [
        f"https://sdk.caict.ac.cn/api/sdk/detail?id={test_record_id}",
        f"https://sdk.caict.ac.cn/api/sdk/detail/{test_record_id}",
        f"https://sdk.caict.ac.cn/api/sdkinfo/detail?id={test_record_id}",
        f"https://sdk.caict.ac.cn/api/sdkinfo/detail/{test_record_id}",
        f"https://sdk.caict.ac.cn/api/public/sdk/detail?id={test_record_id}",
        f"https://sdk.caict.ac.cn/api/public/sdk/detail/{test_record_id}",
        f"https://sdk.caict.ac.cn/api/detail?id={test_record_id}",
        f"https://sdk.caict.ac.cn/api/detail/{test_record_id}",
    ]
    
    # 测试GET请求
    print("测试GET请求:")
    for endpoint in possible_endpoints:
        print(f"\n测试: {endpoint}")
        try:
            response = session.get(endpoint, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
                    
                    if data.get('code') == 100:
                        print("✅ 找到可用的详情API!")
                        return endpoint, 'GET'
                        
                except json.JSONDecodeError:
                    print(f"非JSON响应: {response.text[:200]}...")
            else:
                print(f"HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")
    
    # 测试POST请求
    print("\n" + "=" * 60)
    print("测试POST请求:")
    
    post_endpoints = [
        "https://sdk.caict.ac.cn/api/sdk/detail",
        "https://sdk.caict.ac.cn/api/sdkinfo/detail",
        "https://sdk.caict.ac.cn/api/public/sdk/detail",
        "https://sdk.caict.ac.cn/api/detail",
    ]
    
    for endpoint in post_endpoints:
        print(f"\n测试POST: {endpoint}")
        
        # 尝试不同的payload格式
        payloads = [
            {"id": test_record_id},
            {"recordId": test_record_id},
            {"sdkId": test_record_id},
        ]
        
        for payload in payloads:
            print(f"  payload: {payload}")
            try:
                response = session.post(endpoint, json=payload, timeout=10)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"  响应: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
                        
                        if data.get('code') == 100:
                            print("  ✅ 找到可用的详情API!")
                            return endpoint, 'POST', payload
                            
                    except json.JSONDecodeError:
                        print(f"  非JSON响应: {response.text[:100]}...")
                        
            except Exception as e:
                print(f"  请求失败: {str(e)}")
    
    print("\n❌ 未找到可用的详情API")
    return None

def test_detail_with_multiple_ids():
    """使用多个ID测试详情API"""
    
    # 从现有的JSON文件中读取一些recordId进行测试
    try:
        with open('sdk_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        test_records = data['data'][:5]  # 取前5个记录测试
        
        print("使用多个recordId测试:")
        print("=" * 60)
        
        for record in test_records:
            record_id = record['recordId']
            sdk_name = record['sdkName']
            print(f"\n测试SDK: {sdk_name} (ID: {record_id})")
            
            # 基于之前的分析结果测试
            session = requests.Session()
            main_url = "https://sdk.caict.ac.cn/official/"
            session.get(main_url, timeout=10)
            
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': 'https://sdk.caict.ac.cn',
                'Referer': 'https://sdk.caict.ac.cn/official/',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
            session.headers.update(headers)
            
            # 测试最可能的端点
            test_url = f"https://sdk.caict.ac.cn/api/sdk/detail?id={record_id}"
            
            try:
                response = session.get(test_url, timeout=10)
                if response.status_code == 200:
                    detail_data = response.json()
                    if detail_data.get('code') == 100:
                        print(f"✅ 成功获取详情")
                        detail_info = detail_data.get('data', {})
                        
                        # 查找包名和介绍
                        package_name = detail_info.get('packageName', 'N/A')
                        description = detail_info.get('description', detail_info.get('briefMsg', 'N/A'))
                        
                        print(f"  包名: {package_name}")
                        print(f"  介绍: {description[:100]}..." if len(str(description)) > 100 else f"  介绍: {description}")
                    else:
                        print(f"❌ API错误: {detail_data.get('msg')}")
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求失败: {str(e)}")
            
            time.sleep(1)  # 避免请求过快
            
    except FileNotFoundError:
        print("未找到sdk_data.json文件")
    except Exception as e:
        print(f"读取文件失败: {str(e)}")

if __name__ == "__main__":
    # 先分析API
    result = analyze_detail_api()
    
    if result:
        print(f"\n找到可用API: {result}")
    
    # 然后测试多个ID
    test_detail_with_multiple_ids()
