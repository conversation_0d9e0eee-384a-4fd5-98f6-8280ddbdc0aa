#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作版本的SDK数据爬虫
"""

import requests
import json
import time
import os
from typing import Dict, List, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('working_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class WorkingSDKCrawler:
    def __init__(self):
        self.base_url = "https://sdk.caict.ac.cn/api/sdk/list"
        self.session = requests.Session()
        self.all_data = []
        
        # 设置请求头
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://sdk.caict.ac.cn',
            'Referer': 'https://sdk.caict.ac.cn/official/',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        self.session.headers.update(self.headers)
        
        # 初始化session
        self._init_session()
        
    def _init_session(self) -> None:
        """初始化session"""
        try:
            logging.info("正在初始化session...")
            main_url = "https://sdk.caict.ac.cn/official/"
            response = self.session.get(main_url, timeout=10)
            logging.info(f"访问主页状态码: {response.status_code}")
            
            if self.session.cookies:
                logging.info("成功获取session cookies")
            
        except Exception as e:
            logging.warning(f"初始化session失败: {str(e)}")
    
    def get_page_data(self, page: int = 1, page_size: int = 30) -> Dict[str, Any]:
        """获取指定页面的数据"""
        payload = {
            "platform": "1",
            "pageSize": page_size,
            "page": page,
            "typeId": "",
            "param": "",
            "isCompliance": 0,
            "isViolation": 0,
            "isNotOperate": 0,
            "isVersionUpdate": 0
        }
        
        try:
            logging.info(f"正在抓取第 {page} 页数据...")
            response = self.session.post(self.base_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == 100:
                    list_data = data.get('data', {}).get('list', [])
                    logging.info(f"第 {page} 页抓取成功，获得 {len(list_data)} 条记录")
                    return data
                else:
                    logging.error(f"API返回错误: {data.get('msg', '未知错误')}")
                    return None
            else:
                logging.error(f"HTTP错误: {response.status_code}, 内容: {response.text[:200]}")
                return None
                
        except Exception as e:
            logging.error(f"请求第 {page} 页时发生错误: {str(e)}")
            return None
    
    def crawl_all_pages(self, page_size: int = 30, delay: float = 2.0) -> List[Dict[str, Any]]:
        """抓取所有页面的数据"""
        page = 1
        total_records = 0
        
        while True:
            # 获取当前页数据
            page_data = self.get_page_data(page, page_size)
            
            if not page_data:
                logging.warning(f"第 {page} 页数据获取失败，停止抓取")
                break
            
            data_info = page_data.get('data', {})
            current_page_list = data_info.get('list', [])
            
            if not current_page_list:
                logging.info("当前页没有数据，抓取完成")
                break
            
            # 添加到总数据中
            self.all_data.extend(current_page_list)
            
            # 获取总记录数（第一页时）
            if page == 1:
                total_records = data_info.get('total', 0)
                logging.info(f"总共有 {total_records} 条记录需要抓取")
            
            current_total = len(self.all_data)
            logging.info(f"已抓取 {current_total}/{total_records} 条记录")
            
            # 检查是否已抓取完所有数据
            if current_total >= total_records:
                logging.info("所有数据抓取完成")
                break
            
            # 检查当前页数据量是否小于页面大小（可能是最后一页）
            if len(current_page_list) < page_size:
                logging.info("已到达最后一页")
                break
            
            page += 1
            
            # 添加延迟避免请求过于频繁
            if delay > 0:
                logging.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
        
        logging.info(f"抓取完成，总共获得 {len(self.all_data)} 条SDK数据")
        return self.all_data
    
    def save_to_json(self, filename: str = "sdk_data.json") -> None:
        """将数据保存为JSON文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else '.', exist_ok=True)
            
            # 保存数据
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'total_count': len(self.all_data),
                    'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'platform': 'Android (platform=1)',
                    'data': self.all_data
                }, f, ensure_ascii=False, indent=2)
            
            logging.info(f"数据已保存到 {filename}")
            
        except Exception as e:
            logging.error(f"保存文件时发生错误: {str(e)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        if not self.all_data:
            return {}
        
        # 按类型统计
        type_stats = {}
        company_stats = {}
        
        for item in self.all_data:
            # 类型统计
            type_name = item.get('typeName', '未知')
            type_stats[type_name] = type_stats.get(type_name, 0) + 1
            
            # 公司统计
            company_name = item.get('companyName', '未知')
            company_stats[company_name] = company_stats.get(company_name, 0) + 1
        
        return {
            'total_count': len(self.all_data),
            'type_distribution': dict(sorted(type_stats.items(), key=lambda x: x[1], reverse=True)),
            'top_companies': dict(sorted(company_stats.items(), key=lambda x: x[1], reverse=True)[:10])
        }


def main():
    """主函数"""
    logging.info("开始抓取SDK数据...")
    
    # 创建爬虫实例
    crawler = WorkingSDKCrawler()
    
    try:
        # 抓取所有数据，使用较大的页面大小和较长的延迟
        all_data = crawler.crawl_all_pages(page_size=50, delay=2.0)
        
        if all_data:
            # 保存数据
            crawler.save_to_json("sdk_data.json")
            
            # 显示统计信息
            stats = crawler.get_statistics()
            logging.info("=" * 50)
            logging.info("数据统计信息:")
            logging.info(f"总记录数: {stats.get('total_count', 0)}")
            
            if 'type_distribution' in stats:
                logging.info("\nSDK类型分布:")
                for type_name, count in list(stats['type_distribution'].items())[:5]:
                    logging.info(f"  {type_name}: {count}")
            
            if 'top_companies' in stats:
                logging.info("\n前5大公司:")
                for company, count in list(stats['top_companies'].items())[:5]:
                    logging.info(f"  {company}: {count}")
            
            logging.info("=" * 50)
        else:
            logging.error("没有抓取到任何数据")
            
    except KeyboardInterrupt:
        logging.info("用户中断了抓取过程")
        # 即使中断也保存已抓取的数据
        if crawler.all_data:
            crawler.save_to_json("sdk_data_partial.json")
            logging.info("已保存部分数据到 sdk_data_partial.json")
    except Exception as e:
        logging.error(f"抓取过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
