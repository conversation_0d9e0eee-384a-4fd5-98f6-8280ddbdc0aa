#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正包名 - 尝试通过多种方式获取正确的包名
"""

import requests
import json
import time
import logging
import re
from urllib.parse import urlparse

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

class PackageNameFixer:
    def __init__(self):
        self.session = requests.Session()
        
        # 设置请求头
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://sdk.caict.ac.cn',
            'Referer': 'https://sdk.caict.ac.cn/official/',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        self.session.headers.update(headers)
        
        # 初始化session
        main_url = "https://sdk.caict.ac.cn/official/"
        self.session.get(main_url, timeout=10)
        
        # 已知的正确包名映射
        self.known_packages = {
            "d865d6ed558b4214bdd26cea9116b508": "com.maplehaze.adsdk",  # 枫岚互联SDK
            # 可以添加更多已知的映射
        }
    
    def try_get_real_package_name(self, record_id, sdk_name):
        """尝试通过API获取真实包名"""
        
        # 尝试不同的API端点和参数组合
        api_attempts = [
            # 尝试详情API
            ("https://sdk.caict.ac.cn/api/sdk/detail", {"id": record_id}),
            ("https://sdk.caict.ac.cn/api/sdk/info", {"id": record_id}),
            ("https://sdk.caict.ac.cn/api/sdkinfo/detail", {"recordId": record_id}),
            
            # 尝试公开API
            ("https://sdk.caict.ac.cn/api/public/sdk/detail", {"id": record_id}),
            ("https://sdk.caict.ac.cn/api/public/sdkinfo/detail", {"recordId": record_id}),
        ]
        
        for api_url, params in api_attempts:
            try:
                # 尝试GET请求
                response = self.session.get(api_url, params=params, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('code') == 100:
                        detail_data = data.get('data', {})
                        package_name = detail_data.get('packageName')
                        if package_name:
                            logging.info(f"✅ 通过API获取到 {sdk_name} 的包名: {package_name}")
                            return package_name
                
                # 尝试POST请求
                response = self.session.post(api_url, json=params, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('code') == 100:
                        detail_data = data.get('data', {})
                        package_name = detail_data.get('packageName')
                        if package_name:
                            logging.info(f"✅ 通过API获取到 {sdk_name} 的包名: {package_name}")
                            return package_name
                            
            except Exception as e:
                continue
        
        return None
    
    def improved_package_inference(self, record):
        """改进的包名推断逻辑"""
        
        record_id = record.get('recordId', '')
        sdk_name = (record.get('sdkName') or '').lower()
        company_name = (record.get('companyName') or '').lower()
        official_web = (record.get('officialWeb') or '').lower()
        
        # 首先检查已知映射
        if record_id in self.known_packages:
            return self.known_packages[record_id]
        
        # 尝试从官网URL推断更准确的包名
        if official_web:
            domain = urlparse(official_web).netloc.lower()
            
            # 精确的域名到包名映射
            domain_mappings = {
                'www.maplehaze.cn': 'com.maplehaze.adsdk',
                'maplehaze.cn': 'com.maplehaze.adsdk',
                'www.sharetrace.com': 'com.sharetrace.sdk',
                'sharetrace.com': 'com.sharetrace.sdk',
                'www.umeng.com': 'com.umeng.analytics',
                'umeng.com': 'com.umeng.analytics',
                'mob.com': 'com.mob.sdk',
                'www.mob.com': 'com.mob.sdk',
                'jpush.cn': 'cn.jpush.android',
                'www.jpush.cn': 'cn.jpush.android',
                'getui.com': 'com.igexin.sdk',
                'www.getui.com': 'com.igexin.sdk',
                'sensorsdata.cn': 'com.sensorsdata.analytics.android.sdk',
                'www.sensorsdata.cn': 'com.sensorsdata.analytics.android.sdk',
                'growingio.com': 'com.growingio.android.sdk',
                'www.growingio.com': 'com.growingio.android.sdk',
                'bugly.qq.com': 'com.tencent.bugly',
                'tingyun.com': 'com.tingyun.android',
                'www.tingyun.com': 'com.tingyun.android',
            }
            
            if domain in domain_mappings:
                return domain_mappings[domain]
            
            # 尝试从域名提取包名
            domain_parts = domain.replace('www.', '').split('.')
            if len(domain_parts) >= 2:
                # 对于.com域名
                if domain_parts[-1] == 'com' and len(domain_parts) >= 2:
                    return f"com.{domain_parts[-2]}.sdk"
                # 对于.cn域名
                elif domain_parts[-1] == 'cn' and len(domain_parts) >= 2:
                    return f"cn.{domain_parts[-2]}.sdk"
        
        # 基于公司名的更精确推断
        company_mappings = {
            '华为软件技术有限公司': 'com.huawei.hms',
            '深圳市腾讯计算机系统有限公司': 'com.tencent.mm.opensdk',
            '北京百度网讯科技有限公司': 'com.baidu.mobads',
            '北京火山引擎科技有限公司': 'com.bytedance.sdk',
            '荣耀终端股份有限公司': 'com.honor.sdk',
            '小米科技有限责任公司': 'com.xiaomi.mipush.sdk',
            '广东欢太科技有限公司': 'com.heytap.sdk',
            '北京微播视界科技有限公司': 'com.ss.android.ugc.aweme',
        }
        
        full_company = record.get('companyName', '')
        if full_company in company_mappings:
            return company_mappings[full_company]
        
        # 基于SDK名称的特殊规则
        sdk_name_mappings = {
            'sharetrace': 'com.sharetrace.sdk',
            '个推': 'com.igexin.sdk',
            'jpush': 'cn.jpush.android',
            '极光推送': 'cn.jpush.android',
            'umeng': 'com.umeng.analytics',
            '友盟': 'com.umeng.analytics',
            'bugly': 'com.tencent.bugly',
            'tingyun': 'com.tingyun.android',
            '听云': 'com.tingyun.android',
        }
        
        for keyword, package in sdk_name_mappings.items():
            if keyword in sdk_name:
                return package
        
        # 如果都无法推断，返回None表示未知
        return None
    
    def fix_all_package_names(self, input_file='enhanced_sdk_data.json', output_file='fixed_sdk_data.json'):
        """修正所有包名"""
        
        logging.info(f"开始修正 {input_file} 中的包名...")
        
        # 读取数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        sdk_list = data['data']
        total_count = len(sdk_list)
        
        logging.info(f"共有 {total_count} 条记录需要处理")
        
        fixed_count = 0
        api_success_count = 0
        inference_count = 0
        unknown_count = 0
        
        for i, record in enumerate(sdk_list):
            if i % 100 == 0:
                logging.info(f"已处理 {i}/{total_count} 条记录")
            
            record_id = record.get('recordId', '')
            sdk_name = record.get('sdkName', '')
            
            # 尝试通过API获取真实包名
            real_package = self.try_get_real_package_name(record_id, sdk_name)
            
            if real_package:
                record['packageName'] = real_package
                record['packageSource'] = 'API'
                api_success_count += 1
                fixed_count += 1
            else:
                # 使用改进的推断逻辑
                inferred_package = self.improved_package_inference(record)
                
                if inferred_package:
                    record['packageName'] = inferred_package
                    record['packageSource'] = 'Inferred'
                    inference_count += 1
                    fixed_count += 1
                else:
                    # 移除错误的包名，标记为未知
                    if 'packageName' in record:
                        del record['packageName']
                    record['packageSource'] = 'Unknown'
                    unknown_count += 1
            
            # 添加小延迟避免API请求过快
            if real_package:
                time.sleep(0.1)
        
        # 保存修正后的数据
        data['fix_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
        data['package_fix_stats'] = {
            'total_records': total_count,
            'api_success': api_success_count,
            'inferred': inference_count,
            'unknown': unknown_count,
            'fixed_total': fixed_count
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logging.info(f"修正完成！数据已保存到 {output_file}")
        logging.info(f"统计结果:")
        logging.info(f"  - 通过API获取: {api_success_count}")
        logging.info(f"  - 推断获取: {inference_count}")
        logging.info(f"  - 未知包名: {unknown_count}")
        logging.info(f"  - 总修正数: {fixed_count}/{total_count}")
        
        # 显示一些示例
        logging.info("\n修正示例:")
        for i, record in enumerate(sdk_list[:5]):
            package_name = record.get('packageName', 'Unknown')
            package_source = record.get('packageSource', 'Unknown')
            logging.info(f"{i+1}. {record['sdkName']}")
            logging.info(f"   包名: {package_name} ({package_source})")

def main():
    """主函数"""
    fixer = PackageNameFixer()
    
    try:
        fixer.fix_all_package_names()
        
        logging.info("=" * 50)
        logging.info("✅ 包名修正完成！")
        logging.info("修正后的文件: fixed_sdk_data.json")
        logging.info("=" * 50)
        
    except Exception as e:
        logging.error(f"修正过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
