#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版爬虫 - 尝试获取更多详细信息
"""

import requests
import json
import time
import os
from typing import Dict, List, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EnhancedSDKCrawler:
    def __init__(self):
        self.base_url = "https://sdk.caict.ac.cn/api/sdk/list"
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://sdk.caict.ac.cn',
            'Referer': 'https://sdk.caict.ac.cn/official/',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        self.session.headers.update(self.headers)
        
        # 初始化session
        self._init_session()
        
    def _init_session(self) -> None:
        """初始化session"""
        try:
            logging.info("正在初始化session...")
            main_url = "https://sdk.caict.ac.cn/official/"
            response = self.session.get(main_url, timeout=10)
            logging.info(f"访问主页状态码: {response.status_code}")
            
        except Exception as e:
            logging.warning(f"初始化session失败: {str(e)}")
    
    def get_enhanced_sdk_list(self, page: int = 1, page_size: int = 30) -> Dict[str, Any]:
        """获取增强的SDK列表数据"""
        payload = {
            "platform": "1",
            "pageSize": page_size,
            "page": page,
            "typeId": "",
            "param": "",
            "isCompliance": 0,
            "isViolation": 0,
            "isNotOperate": 0,
            "isVersionUpdate": 0
        }
        
        try:
            logging.info(f"正在抓取第 {page} 页数据...")
            response = self.session.post(self.base_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == 100:
                    list_data = data.get('data', {}).get('list', [])
                    logging.info(f"第 {page} 页抓取成功，获得 {len(list_data)} 条记录")
                    return data
                else:
                    logging.error(f"API返回错误: {data.get('msg', '未知错误')}")
                    return None
            else:
                logging.error(f"HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"请求第 {page} 页时发生错误: {str(e)}")
            return None
    
    def try_get_detail_info(self, record_id: str, sdk_name: str) -> Dict[str, Any]:
        """尝试获取SDK详细信息"""
        
        # 尝试多种可能的详情API
        detail_apis = [
            # 基于列表API的变体
            "https://sdk.caict.ac.cn/api/sdk/detail",
            "https://sdk.caict.ac.cn/api/sdk/info", 
            "https://sdk.caict.ac.cn/api/sdkinfo/detail",
            "https://sdk.caict.ac.cn/api/sdkinfo/info",
            # 公开API尝试
            "https://sdk.caict.ac.cn/api/public/sdk/detail",
            "https://sdk.caict.ac.cn/api/public/sdkinfo/detail",
        ]
        
        for api_url in detail_apis:
            # 尝试不同的参数格式
            param_formats = [
                {"id": record_id},
                {"recordId": record_id},
                {"sdkId": record_id},
            ]
            
            for params in param_formats:
                try:
                    # 尝试GET请求
                    response = self.session.get(api_url, params=params, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('code') == 100:
                            logging.info(f"✅ 通过GET获取到 {sdk_name} 的详情")
                            return data.get('data', {})
                    
                    # 尝试POST请求
                    response = self.session.post(api_url, json=params, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('code') == 100:
                            logging.info(f"✅ 通过POST获取到 {sdk_name} 的详情")
                            return data.get('data', {})
                            
                except Exception as e:
                    continue
        
        # 如果都失败了，返回空字典
        return {}
    
    def enhance_sdk_data(self, sdk_record: Dict[str, Any]) -> Dict[str, Any]:
        """增强SDK数据"""
        
        record_id = sdk_record.get('recordId', '')
        sdk_name = sdk_record.get('sdkName', '')
        
        # 尝试获取详细信息
        detail_info = self.try_get_detail_info(record_id, sdk_name)
        
        # 合并详细信息到原记录
        enhanced_record = sdk_record.copy()
        
        if detail_info:
            # 添加可能的新字段
            potential_fields = [
                'packageName', 'description', 'detailDescription', 
                'fullDescription', 'technicalDescription', 'features',
                'permissions', 'dependencies', 'changelog', 'documentation'
            ]
            
            for field in potential_fields:
                if field in detail_info:
                    enhanced_record[field] = detail_info[field]
                    logging.info(f"为 {sdk_name} 添加了字段: {field}")
        
        # 如果没有获取到详细信息，尝试从现有字段推断更多信息
        if not detail_info:
            # 尝试从官方网站URL推断包名
            official_web = sdk_record.get('officialWeb', '')
            if official_web:
                # 简单的包名推断逻辑
                if 'maplehaze' in official_web:
                    enhanced_record['inferredPackageName'] = 'com.maplehaze.adsdk'
                elif 'sharetrace' in official_web:
                    enhanced_record['inferredPackageName'] = 'com.sharetrace.sdk'
                elif 'baidu' in official_web:
                    enhanced_record['inferredPackageName'] = 'com.baidu.sdk'
                elif 'tencent' in official_web or 'qq.com' in official_web:
                    enhanced_record['inferredPackageName'] = 'com.tencent.sdk'
                elif 'huawei' in official_web:
                    enhanced_record['inferredPackageName'] = 'com.huawei.sdk'
        
        return enhanced_record
    
    def crawl_enhanced_data(self, page_size: int = 30, delay: float = 2.0, max_pages: int = None) -> List[Dict[str, Any]]:
        """抓取增强的SDK数据"""
        
        all_data = []
        page = 1
        total_records = 0
        
        while True:
            if max_pages and page > max_pages:
                logging.info(f"达到最大页数限制 {max_pages}")
                break
                
            # 获取当前页数据
            page_data = self.get_enhanced_sdk_list(page, page_size)
            
            if not page_data:
                logging.warning(f"第 {page} 页数据获取失败，停止抓取")
                break
            
            data_info = page_data.get('data', {})
            current_page_list = data_info.get('list', [])
            
            if not current_page_list:
                logging.info("当前页没有数据，抓取完成")
                break
            
            # 获取总记录数（第一页时）
            if page == 1:
                total_records = data_info.get('total', 0)
                logging.info(f"总共有 {total_records} 条记录需要抓取")
            
            # 增强每条记录的数据
            enhanced_records = []
            for i, record in enumerate(current_page_list):
                logging.info(f"正在增强第 {page} 页第 {i+1} 条记录: {record.get('sdkName', 'Unknown')}")
                
                enhanced_record = self.enhance_sdk_data(record)
                enhanced_records.append(enhanced_record)
                
                # 添加小延迟避免请求过快
                time.sleep(0.5)
            
            all_data.extend(enhanced_records)
            
            current_total = len(all_data)
            logging.info(f"已抓取 {current_total}/{total_records} 条记录")
            
            # 检查是否已抓取完所有数据
            if current_total >= total_records:
                logging.info("所有数据抓取完成")
                break
            
            # 检查当前页数据量是否小于页面大小（可能是最后一页）
            if len(current_page_list) < page_size:
                logging.info("已到达最后一页")
                break
            
            page += 1
            
            # 添加延迟避免请求过于频繁
            if delay > 0:
                logging.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
        
        logging.info(f"增强抓取完成，总共获得 {len(all_data)} 条SDK数据")
        return all_data
    
    def save_enhanced_data(self, data: List[Dict[str, Any]], filename: str = "enhanced_sdk_data.json") -> None:
        """保存增强的数据"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'total_count': len(data),
                    'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'platform': 'Android (platform=1)',
                    'enhanced': True,
                    'data': data
                }, f, ensure_ascii=False, indent=2)
            
            logging.info(f"增强数据已保存到 {filename}")
            
        except Exception as e:
            logging.error(f"保存文件时发生错误: {str(e)}")


def main():
    """主函数"""
    logging.info("开始增强抓取SDK数据...")
    
    # 创建增强爬虫实例
    crawler = EnhancedSDKCrawler()
    
    try:
        # 先测试少量数据
        logging.info("测试模式：只抓取前2页数据")
        enhanced_data = crawler.crawl_enhanced_data(page_size=30, delay=2.0, max_pages=2)
        
        if enhanced_data:
            # 保存增强数据
            crawler.save_enhanced_data(enhanced_data, "enhanced_sdk_data_test.json")
            
            # 显示增强结果
            logging.info("=" * 50)
            logging.info("增强结果统计:")
            
            enhanced_count = 0
            inferred_package_count = 0
            
            for record in enhanced_data:
                if any(key not in ['recordId', 'sdkId', 'sdkName', 'typeName', 'companyName', 'version', 'installationAmount', 'platform', 'officialWeb', 'sdkDocumentUrl', 'privacyPolicyUrl', 'briefMsg', 'isTop', 'createTime', 'updateTime', 'isCompliance', 'sdkComplianceInstructionsUrl', 'msgType', 'cornerMark', 'riskSdkData', 'typeId', 'companyId'] for key in record.keys()):
                    enhanced_count += 1
                
                if 'inferredPackageName' in record:
                    inferred_package_count += 1
            
            logging.info(f"成功增强的记录数: {enhanced_count}")
            logging.info(f"推断出包名的记录数: {inferred_package_count}")
            
            # 显示一些示例
            if enhanced_data:
                logging.info("\n示例增强记录:")
                for i, record in enumerate(enhanced_data[:3]):
                    logging.info(f"记录 {i+1}: {record.get('sdkName', 'Unknown')}")
                    if 'inferredPackageName' in record:
                        logging.info(f"  推断包名: {record['inferredPackageName']}")
                    
            logging.info("=" * 50)
        else:
            logging.error("没有抓取到任何数据")
            
    except KeyboardInterrupt:
        logging.info("用户中断了抓取过程")
    except Exception as e:
        logging.error(f"抓取过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
