#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更多可能的API端点
"""

import requests
import json

def test_more_endpoints():
    """测试更多可能的API端点"""
    
    session = requests.Session()
    
    # 建立session
    main_url = "https://sdk.caict.ac.cn/official/"
    session.get(main_url, timeout=10)
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    session.headers.update(headers)
    
    # 基于已知的工作端点，尝试更多变体
    working_base = "https://sdk.caict.ac.cn/api"
    
    # 可能的端点路径
    possible_paths = [
        "/sdkinfo/list",           # 从JS中找到的
        "/public/sdkinfo/list",    # 公开的SDK信息列表
        "/sdklist/info",           # 你提供的原始路径
        "/public/sdklist/info",    # 公开的SDK列表信息
        "/sdk/list",               # 简化的SDK列表
        "/public/sdk/list",        # 公开的SDK列表
        "/list",                   # 最简单的列表
        "/public/list",            # 公开列表
        "/sdkinfo/search",         # SDK信息搜索
        "/public/sdkinfo/search",  # 公开SDK信息搜索
        "/sdk/search",             # SDK搜索
        "/public/sdk/search",      # 公开SDK搜索
        "/hotBrowse/list",         # 热门浏览列表（从JS中找到的）
        "/public/hotBrowse/list",  # 公开热门浏览列表
    ]
    
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    print("测试更多可能的API端点...")
    print("=" * 60)
    
    successful_endpoints = []
    
    for path in possible_paths:
        full_url = working_base + path
        print(f"\n测试: {full_url}")
        
        try:
            response = session.post(full_url, json=payload, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    code = data.get('code')
                    msg = data.get('msg')
                    print(f"响应代码: {code}, 消息: {msg}")
                    
                    if code == 100:
                        data_info = data.get('data', {})
                        total = data_info.get('total', 0)
                        current_list = data_info.get('list', [])
                        
                        print(f"✅ 成功! 总记录数: {total}, 当前页: {len(current_list)}")
                        
                        if current_list:
                            first_record = current_list[0]
                            print(f"第一条记录的SDK名称: {first_record.get('sdkName', 'N/A')}")
                            print(f"公司名称: {first_record.get('companyName', 'N/A')}")
                            print(f"类型: {first_record.get('typeName', 'N/A')}")
                        
                        successful_endpoints.append({
                            'url': full_url,
                            'total': total,
                            'current_count': len(current_list)
                        })
                    else:
                        print(f"API错误: {data}")
                        
                except json.JSONDecodeError:
                    print(f"非JSON响应: {response.text[:100]}...")
            else:
                print(f"HTTP错误 {response.status_code}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("成功的端点总结:")
    
    if successful_endpoints:
        for endpoint in successful_endpoints:
            print(f"✅ {endpoint['url']}")
            print(f"   总记录数: {endpoint['total']}, 当前页记录数: {endpoint['current_count']}")
        
        # 找到记录数最多的端点
        best_endpoint = max(successful_endpoints, key=lambda x: x['total'])
        print(f"\n🎯 推荐使用的端点 (记录数最多): {best_endpoint['url']}")
        print(f"   总记录数: {best_endpoint['total']}")
        
        return best_endpoint['url']
    else:
        print("❌ 没有找到成功的端点")
        return None

def test_endpoint_with_different_params(endpoint_url):
    """测试端点的不同参数组合"""
    
    if not endpoint_url:
        return
    
    print(f"\n测试端点 {endpoint_url} 的不同参数...")
    print("=" * 60)
    
    session = requests.Session()
    main_url = "https://sdk.caict.ac.cn/official/"
    session.get(main_url, timeout=10)
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    session.headers.update(headers)
    
    # 测试不同的参数组合
    test_params = [
        # 原始参数
        {
            "platform": "1",
            "pageSize": 30,
            "page": 1,
            "typeId": "",
            "param": "",
            "isCompliance": 0,
            "isViolation": 0,
            "isNotOperate": 0,
            "isVersionUpdate": 0
        },
        # 尝试更大的页面大小
        {
            "platform": "1",
            "pageSize": 100,
            "page": 1,
            "typeId": "",
            "param": "",
            "isCompliance": 0,
            "isViolation": 0,
            "isNotOperate": 0,
            "isVersionUpdate": 0
        },
        # 尝试不同的平台
        {
            "platform": "2",  # 可能是iOS
            "pageSize": 30,
            "page": 1,
            "typeId": "",
            "param": "",
            "isCompliance": 0,
            "isViolation": 0,
            "isNotOperate": 0,
            "isVersionUpdate": 0
        }
    ]
    
    for i, params in enumerate(test_params, 1):
        print(f"\n测试参数组合 {i}: {params}")
        
        try:
            response = session.post(endpoint_url, json=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 100:
                    data_info = data.get('data', {})
                    total = data_info.get('total', 0)
                    current_list = data_info.get('list', [])
                    print(f"✅ 成功! 总记录数: {total}, 当前页: {len(current_list)}")
                else:
                    print(f"API错误: {data}")
            else:
                print(f"HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    best_endpoint = test_more_endpoints()
    test_endpoint_with_different_params(best_endpoint)
