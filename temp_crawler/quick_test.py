#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试API是否还能工作
"""

import requests
import json

def quick_test():
    session = requests.Session()
    
    # 访问主页
    main_url = "https://sdk.caict.ac.cn/official/"
    session.get(main_url, timeout=10)
    
    # 设置请求头
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    session.headers.update(headers)
    
    # 测试API
    url = "https://sdk.caict.ac.cn/api/sdk/list"
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    print(f"测试API: {url}")
    print(f"Cookies: {dict(session.cookies)}")
    
    try:
        response = session.post(url, json=payload, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
        else:
            print(f"响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    quick_test()
