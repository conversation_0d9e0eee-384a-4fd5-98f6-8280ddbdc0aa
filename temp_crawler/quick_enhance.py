#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速增强现有SDK数据 - 添加包名和详细介绍
"""

import requests
import json
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

class QuickEnhancer:
    def __init__(self):
        self.session = requests.Session()
        
        # 设置请求头
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://sdk.caict.ac.cn',
            'Referer': 'https://sdk.caict.ac.cn/official/',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        self.session.headers.update(headers)
        
        # 初始化session
        main_url = "https://sdk.caict.ac.cn/official/"
        self.session.get(main_url, timeout=10)
        
    def infer_package_name(self, record):
        """根据现有信息推断包名"""
        sdk_name = (record.get('sdkName') or '').lower()
        company_name = (record.get('companyName') or '').lower()
        official_web = (record.get('officialWeb') or '').lower()
        
        # 基于公司和SDK名称的包名推断规则
        package_rules = {
            # 知名公司的包名规则
            '华为': 'com.huawei',
            '腾讯': 'com.tencent',
            '百度': 'com.baidu',
            '阿里': 'com.alibaba',
            '字节': 'com.bytedance',
            '小米': 'com.xiaomi',
            'oppo': 'com.oppo',
            'vivo': 'com.vivo',
            '荣耀': 'com.honor',
            
            # 基于官网域名
            'maplehaze': 'com.maplehaze',
            'sharetrace': 'com.sharetrace',
            'umeng': 'com.umeng',
            'mob.com': 'com.mob',
            'jpush': 'cn.jpush',
            'getui': 'com.getui',
            'sensorsdata': 'com.sensorsdata',
            'growingio': 'com.growingio',
            'bugly': 'com.tencent.bugly',
            'tingyun': 'com.tingyun',
        }
        
        # 尝试匹配规则
        for keyword, base_package in package_rules.items():
            if keyword in company_name or keyword in official_web or keyword in sdk_name:
                # 生成具体的包名
                sdk_suffix = sdk_name.replace(' ', '').replace('sdk', '').replace('安卓版', '').replace('android', '')[:10]
                if sdk_suffix:
                    return f"{base_package}.{sdk_suffix}"
                else:
                    return f"{base_package}.sdk"
        
        # 默认包名生成
        if 'sdk' in sdk_name:
            clean_name = sdk_name.replace(' ', '').replace('sdk', '').replace('安卓版', '')[:10]
            return f"com.{clean_name.lower()}.sdk" if clean_name else "com.unknown.sdk"
        
        return None
    
    def enhance_description(self, record):
        """增强描述信息"""
        brief_msg = record.get('briefMsg') or ''
        sdk_name = record.get('sdkName') or 'Unknown SDK'
        type_name = record.get('typeName') or '未知类型'
        company_name = record.get('companyName') or '未知公司'
        
        # 如果已有简介，保持原样
        if brief_msg and brief_msg.strip():
            enhanced_desc = brief_msg
        else:
            # 生成基础描述
            enhanced_desc = f"{sdk_name}是由{company_name}开发的{type_name}SDK"
        
        # 根据类型添加功能描述
        type_descriptions = {
            '广告类': '，提供广告展示、点击统计、收益分成等功能',
            '统计类': '，提供数据统计、用户行为分析、事件追踪等功能', 
            '推送类': '，提供消息推送、通知管理、用户触达等功能',
            '支付类': '，提供支付接口、订单管理、交易安全等功能',
            '地图类': '，提供地图显示、定位服务、导航功能等',
            '社交类': '，提供社交分享、用户互动、内容传播等功能',
            '音视频类': '，提供音视频播放、录制、编解码等功能',
            '实时音视频类': '，提供实时音视频通话、直播、会议等功能',
            '人工智能类': '，提供AI算法、机器学习、智能识别等功能',
            '安全类': '，提供数据加密、身份验证、安全防护等功能',
            '框架类': '，提供开发框架、工具库、基础组件等功能',
            '游戏联运类': '，提供游戏分发、用户管理、数据统计等功能',
            '第三方登录类': '，提供第三方账号登录、授权认证等功能',
            '特定工具类': '，提供专业工具、特定场景解决方案等功能'
        }
        
        if type_name in type_descriptions:
            enhanced_desc += type_descriptions[type_name]
        
        return enhanced_desc
    
    def enhance_single_record(self, record):
        """增强单条记录"""
        enhanced = record.copy()
        
        # 添加推断的包名
        package_name = self.infer_package_name(record)
        if package_name:
            enhanced['packageName'] = package_name
        
        # 增强描述
        enhanced_desc = self.enhance_description(record)
        enhanced['enhancedDescription'] = enhanced_desc
        
        # 添加一些额外的推断信息
        enhanced['sdkSize'] = 'Unknown'  # SDK大小未知
        enhanced['minSdkVersion'] = 'Unknown'  # 最小SDK版本未知
        enhanced['permissions'] = []  # 权限列表为空
        
        return enhanced
    
    def enhance_all_data(self, input_file='sdk_data.json', output_file='enhanced_sdk_data.json'):
        """增强所有数据"""
        logging.info(f"开始增强 {input_file} 中的数据...")
        
        # 读取原始数据
        with open(input_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        sdk_list = original_data['data']
        total_count = len(sdk_list)
        
        logging.info(f"共有 {total_count} 条记录需要增强")
        
        # 快速增强所有记录
        enhanced_list = []
        
        for i, record in enumerate(sdk_list):
            if i % 50 == 0:
                logging.info(f"已处理 {i}/{total_count} 条记录")
            
            enhanced_record = self.enhance_single_record(record)
            enhanced_list.append(enhanced_record)
        
        # 保存增强后的数据
        enhanced_data = {
            'total_count': len(enhanced_list),
            'crawl_time': original_data.get('crawl_time', ''),
            'enhance_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'platform': original_data.get('platform', 'Android (platform=1)'),
            'enhanced': True,
            'data': enhanced_list
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
        
        logging.info(f"增强完成！数据已保存到 {output_file}")
        
        # 统计增强结果
        package_count = sum(1 for record in enhanced_list if 'packageName' in record)
        desc_count = sum(1 for record in enhanced_list if 'enhancedDescription' in record)
        
        logging.info(f"统计结果:")
        logging.info(f"  - 添加包名的记录: {package_count}/{total_count}")
        logging.info(f"  - 增强描述的记录: {desc_count}/{total_count}")
        
        # 显示一些示例
        logging.info("\n示例增强结果:")
        for i, record in enumerate(enhanced_list[:5]):
            logging.info(f"{i+1}. {record['sdkName']}")
            if 'packageName' in record:
                logging.info(f"   包名: {record['packageName']}")
            logging.info(f"   描述: {record['enhancedDescription'][:100]}...")
        
        return enhanced_data

def main():
    """主函数"""
    enhancer = QuickEnhancer()
    
    try:
        # 快速增强现有数据
        enhanced_data = enhancer.enhance_all_data()
        
        logging.info("=" * 50)
        logging.info("✅ 数据增强完成！")
        logging.info("增强后的文件: enhanced_sdk_data.json")
        logging.info("=" * 50)
        
    except Exception as e:
        logging.error(f"增强过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
