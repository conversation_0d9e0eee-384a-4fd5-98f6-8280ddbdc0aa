#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于JavaScript分析结果测试真实API
"""

import requests
import json

def test_real_endpoints():
    """测试从JavaScript中找到的真实API端点"""
    
    session = requests.Session()
    
    # 先访问主页建立session
    print("步骤1: 建立session...")
    try:
        main_url = "https://sdk.caict.ac.cn/official/"
        response = session.get(main_url, timeout=10)
        print(f"主页访问状态码: {response.status_code}")
        print(f"获取到的cookies: {dict(session.cookies)}")
    except Exception as e:
        print(f"访问主页失败: {str(e)}")
    
    # 设置API请求头
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    session.headers.update(headers)
    
    # 基于JavaScript分析的真实端点
    base_urls = [
        "https://sdk.caict.ac.cn",
        "https://sdk.caict.ac.cn/official"
    ]
    
    api_paths = [
        "/sdkinfo/list",
        "/api/sdkinfo/list", 
        "/official/api/sdkinfo/list",
        "/compliance/list",
        "/api/compliance/list",
        "/official/api/compliance/list",
        "/compliance/latest/list",
        "/api/compliance/latest/list",
        "/official/api/compliance/latest/list"
    ]
    
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    print("\n步骤2: 测试真实API端点...")
    print("=" * 60)
    
    for base_url in base_urls:
        for path in api_paths:
            full_url = base_url + path
            print(f"\n测试: {full_url}")
            
            try:
                response = session.post(full_url, json=payload, timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"响应代码: {data.get('code')}")
                        print(f"响应消息: {data.get('msg')}")
                        
                        if data.get('code') == 100:
                            print("🎉 成功找到可用的API端点!")
                            data_info = data.get('data', {})
                            total = data_info.get('total', 0)
                            current_list = data_info.get('list', [])
                            print(f"总记录数: {total}")
                            print(f"当前页记录数: {len(current_list)}")
                            
                            if current_list:
                                print("第一条记录示例:")
                                first_record = current_list[0]
                                for key, value in first_record.items():
                                    if isinstance(value, str) and len(value) > 50:
                                        print(f"  {key}: {value[:50]}...")
                                    else:
                                        print(f"  {key}: {value}")
                            
                            return full_url
                        else:
                            print(f"API返回错误: {data}")
                            
                    except json.JSONDecodeError:
                        print(f"响应内容: {response.text[:200]}...")
                else:
                    print(f"HTTP错误: {response.text[:100]}...")
                    
            except Exception as e:
                print(f"请求失败: {str(e)}")
    
    print("\n❌ 仍未找到可用的API端点")
    
    # 尝试不同的请求方法
    print("\n步骤3: 尝试GET请求...")
    print("=" * 60)
    
    for base_url in base_urls:
        for path in api_paths:
            full_url = base_url + path
            print(f"\n测试GET: {full_url}")
            
            try:
                params = {
                    'platform': '1',
                    'pageSize': '30',
                    'page': '1',
                    'typeId': '',
                    'param': '',
                    'isCompliance': '0',
                    'isViolation': '0',
                    'isNotOperate': '0',
                    'isVersionUpdate': '0'
                }
                
                response = session.get(full_url, params=params, timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 100:
                            print("🎉 GET请求成功!")
                            return full_url
                        else:
                            print(f"GET响应: {data}")
                    except:
                        print(f"GET响应内容: {response.text[:100]}...")
                        
            except Exception as e:
                print(f"GET请求失败: {str(e)}")
    
    return None

if __name__ == "__main__":
    result = test_real_endpoints()
    if result:
        print(f"\n✅ 找到可用端点: {result}")
    else:
        print("\n❌ 未找到可用端点")
