#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行SDK爬虫的简单脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sdk_crawler import main

if __name__ == "__main__":
    print("开始运行SDK数据爬虫...")
    print("=" * 50)
    main()
    print("=" * 50)
    print("爬虫运行完成！")
    print("数据已保存到 temp_crawler/sdk_data.json")
    print("日志已保存到 temp_crawler/sdk_crawler.log")
