#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SDK API的脚本
"""

import requests
import json

def test_api():
    """测试API端点"""

    # 根据你提供的信息，尝试更多可能的URL
    urls = [
        "https://sdk.caict.ac.cn/official/api/public/sdklist/info",
        "https://sdk.caict.ac.cn/api/public/sdklist/info",
        "https://sdk.caict.ac.cn/official/public/sdklist/info",
        "https://sdk.caict.ac.cn/public/sdklist/info",
        "https://sdk.caict.ac.cn/official/api/sdklist/info",
        "https://sdk.caict.ac.cn/api/sdklist/info",
        # 尝试不同的路径
        "https://sdk.caict.ac.cn/official/api/public/sdk/list",
        "https://sdk.caict.ac.cn/api/public/sdk/list",
        "https://sdk.caict.ac.cn/official/api/public/sdklist",
        "https://sdk.caict.ac.cn/api/public/sdklist"
    ]
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/#/public/sdklist/info/android',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    for url in urls:
        print(f"\n测试URL: {url}")
        print("-" * 50)
        
        # 先尝试POST请求
        try:
            print("尝试POST请求...")
            response = session.post(url, json=payload, timeout=10)
            print(f"POST状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"POST响应数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
                    if data.get('code') == 100:
                        print("✅ POST请求成功找到可用的API端点!")
                        return url
                except json.JSONDecodeError:
                    print(f"POST响应内容: {response.text[:200]}...")
            else:
                print(f"POST响应内容: {response.text[:200]}...")

        except Exception as e:
            print(f"POST请求错误: {str(e)}")

        # 再尝试GET请求
        try:
            print("尝试GET请求...")
            params = {
                'platform': '1',
                'pageSize': '30',
                'page': '1',
                'typeId': '',
                'param': '',
                'isCompliance': '0',
                'isViolation': '0',
                'isNotOperate': '0',
                'isVersionUpdate': '0'
            }
            response = session.get(url, params=params, timeout=10)
            print(f"GET状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"GET响应数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
                    if data.get('code') == 100:
                        print("✅ GET请求成功找到可用的API端点!")
                        return url
                except json.JSONDecodeError:
                    print(f"GET响应内容: {response.text[:200]}...")
            else:
                print(f"GET响应内容: {response.text[:200]}...")

        except Exception as e:
            print(f"GET请求错误: {str(e)}")
    
    print("\n❌ 没有找到可用的API端点")
    return None

if __name__ == "__main__":
    test_api()
