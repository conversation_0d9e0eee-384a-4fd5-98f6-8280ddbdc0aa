#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过分析HTML页面来获取SDK数据
"""

import requests
import json
import re
import time
from bs4 import BeautifulSoup

def analyze_page():
    """分析页面内容，寻找API调用或数据"""
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
    }
    
    session.headers.update(headers)
    
    print("正在分析页面...")
    
    try:
        # 获取主页面
        url = "https://sdk.caict.ac.cn/official/"
        response = session.get(url, timeout=10)
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            html_content = response.text
            
            # 查找JavaScript中的API调用
            print("\n查找JavaScript中的API端点...")
            
            # 查找可能的API URL
            api_patterns = [
                r'["\']([^"\']*api[^"\']*sdklist[^"\']*)["\']',
                r'["\']([^"\']*sdklist[^"\']*info[^"\']*)["\']',
                r'baseURL["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'apiUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'url["\']?\s*[:=]\s*["\']([^"\']*api[^"\']*)["\']'
            ]
            
            found_urls = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if 'api' in match.lower() or 'sdk' in match.lower():
                        found_urls.add(match)
            
            if found_urls:
                print("找到可能的API端点:")
                for url in found_urls:
                    print(f"  - {url}")
            else:
                print("没有在JavaScript中找到明显的API端点")
            
            # 查找内联的数据
            print("\n查找页面中的内联数据...")
            
            # 查找可能的JSON数据
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__DATA__\s*=\s*({.*?});',
                r'var\s+initialData\s*=\s*({.*?});',
                r'const\s+initialData\s*=\s*({.*?});'
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL)
                if matches:
                    print(f"找到初始数据: {matches[0][:200]}...")
            
            # 保存HTML内容用于进一步分析
            with open('page_source.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("\n页面源码已保存到 page_source.html")
            
            # 查找所有script标签
            soup = BeautifulSoup(html_content, 'html.parser')
            scripts = soup.find_all('script')
            
            print(f"\n找到 {len(scripts)} 个script标签")
            
            for i, script in enumerate(scripts):
                if script.string and len(script.string) > 100:
                    # 查找包含API相关内容的脚本
                    script_content = script.string
                    if any(keyword in script_content.lower() for keyword in ['api', 'sdk', 'request', 'axios', 'fetch']):
                        print(f"\nScript {i+1} (包含API相关内容):")
                        print(script_content[:500] + "..." if len(script_content) > 500 else script_content)
                        
                        # 保存这个脚本
                        with open(f'script_{i+1}.js', 'w', encoding='utf-8') as f:
                            f.write(script_content)
                        print(f"脚本已保存到 script_{i+1}.js")
        
    except Exception as e:
        print(f"分析页面失败: {str(e)}")

def try_different_endpoints():
    """尝试基于分析结果的不同端点"""
    
    # 基于常见的API模式尝试更多端点
    base_urls = [
        "https://sdk.caict.ac.cn",
        "https://api.sdk.caict.ac.cn", 
        "https://sdk.caict.ac.cn/official"
    ]
    
    paths = [
        "/api/public/sdklist/info",
        "/api/v1/public/sdklist/info", 
        "/api/v2/public/sdklist/info",
        "/public/api/sdklist/info",
        "/official/api/public/sdklist/info",
        "/rest/public/sdklist/info",
        "/service/public/sdklist/info"
    ]
    
    session = requests.Session()
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    session.headers.update(headers)
    
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    print("\n尝试更多API端点组合...")
    
    for base_url in base_urls:
        for path in paths:
            full_url = base_url + path
            print(f"\n测试: {full_url}")
            
            try:
                response = session.post(full_url, json=payload, timeout=5)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 100:
                            print("✅ 找到可用端点!")
                            print(f"数据: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
                            return full_url
                        else:
                            print(f"响应: {data}")
                    except:
                        print(f"响应内容: {response.text[:100]}...")
                        
            except Exception as e:
                print(f"请求失败: {str(e)}")
    
    print("\n❌ 仍未找到可用端点")

if __name__ == "__main__":
    analyze_page()
    try_different_endpoints()
