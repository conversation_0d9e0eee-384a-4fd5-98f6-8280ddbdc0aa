#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析JavaScript文件找到API端点
"""

import requests
import re
import json

def download_and_analyze_js():
    """下载并分析JavaScript文件"""
    
    session = requests.Session()
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://sdk.caict.ac.cn/official/'
    }
    session.headers.update(headers)
    
    # JavaScript文件URL
    js_url = "https://sdk.caict.ac.cn/official/umi.15ad608c.js"
    
    print(f"正在下载JavaScript文件: {js_url}")
    
    try:
        response = session.get(js_url, timeout=30)
        print(f"下载状态码: {response.status_code}")
        
        if response.status_code == 200:
            js_content = response.text
            print(f"JavaScript文件大小: {len(js_content)} 字符")
            
            # 保存JavaScript文件
            with open('umi.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            print("JavaScript文件已保存到 umi.js")
            
            # 分析JavaScript内容
            analyze_js_content(js_content)
            
        else:
            print(f"下载失败: {response.status_code}")
            
    except Exception as e:
        print(f"下载JavaScript文件失败: {str(e)}")

def analyze_js_content(js_content):
    """分析JavaScript内容寻找API端点"""
    
    print("\n正在分析JavaScript内容...")
    
    # 查找API相关的模式
    patterns = [
        # API端点
        r'["\']([^"\']*api[^"\']*sdklist[^"\']*)["\']',
        r'["\']([^"\']*sdklist[^"\']*info[^"\']*)["\']',
        r'["\']([^"\']*\/api\/[^"\']*)["\']',
        
        # 基础URL
        r'baseURL["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        r'apiUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        r'BASE_URL["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        
        # 请求配置
        r'url["\']?\s*[:=]\s*["\']([^"\']*api[^"\']*)["\']',
        r'endpoint["\']?\s*[:=]\s*["\']([^"\']*)["\']',
        
        # 可能的域名
        r'["\']https?://[^"\']*caict[^"\']*["\']',
        
        # 查找包含"sdklist"的字符串
        r'["\'][^"\']*sdklist[^"\']*["\']',
        
        # 查找包含"public"的API路径
        r'["\'][^"\']*public[^"\']*api[^"\']*["\']',
        r'["\'][^"\']*api[^"\']*public[^"\']*["\']'
    ]
    
    found_urls = set()
    
    for pattern in patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if len(match) > 5:  # 过滤太短的匹配
                found_urls.add(match)
    
    if found_urls:
        print("\n找到可能的API相关字符串:")
        for url in sorted(found_urls):
            print(f"  - {url}")
    else:
        print("没有找到明显的API端点")
    
    # 查找特定的关键词周围的内容
    keywords = ['sdklist', 'api', 'public', 'info', 'platform']
    
    print("\n查找关键词周围的上下文:")
    for keyword in keywords:
        pattern = rf'.{{0,50}}{keyword}.{{0,50}}'
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        if matches:
            print(f"\n关键词 '{keyword}' 的上下文:")
            for i, match in enumerate(matches[:5]):  # 只显示前5个匹配
                print(f"  {i+1}: ...{match}...")
    
    # 查找可能的请求函数调用
    print("\n查找可能的HTTP请求:")
    request_patterns = [
        r'(axios\.[^;]+)',
        r'(fetch\([^)]+\))',
        r'(\$\.post\([^)]+\))',
        r'(\$\.get\([^)]+\))',
        r'(request\([^)]+\))'
    ]
    
    for pattern in request_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        if matches:
            print(f"找到请求调用:")
            for match in matches[:3]:  # 只显示前3个
                print(f"  - {match}")

def test_found_endpoints():
    """测试找到的端点"""
    
    # 基于分析结果，尝试一些可能的端点
    possible_endpoints = [
        "https://sdk.caict.ac.cn/api/public/sdklist/info",
        "https://sdk.caict.ac.cn/official/api/public/sdklist/info", 
        "https://sdk.caict.ac.cn/api/sdklist/info",
        "https://sdk.caict.ac.cn/public/sdklist/info"
    ]
    
    session = requests.Session()
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    session.headers.update(headers)
    
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    print("\n\n测试找到的端点:")
    print("=" * 50)
    
    for endpoint in possible_endpoints:
        print(f"\n测试: {endpoint}")
        try:
            response = session.post(endpoint, json=payload, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('code') == 100:
                        print("✅ 成功!")
                        print(f"数据: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
                        return endpoint
                    else:
                        print(f"响应: {data}")
                except:
                    print(f"响应内容: {response.text[:100]}...")
        except Exception as e:
            print(f"请求失败: {str(e)}")
    
    return None

if __name__ == "__main__":
    download_and_analyze_js()
    test_found_endpoints()
