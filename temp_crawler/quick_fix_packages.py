#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修正包名 - 使用改进的推断逻辑
"""

import json
import time
import logging
from urllib.parse import urlparse

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

class QuickPackageFixer:
    def __init__(self):
        # 已知的正确包名映射（基于你提供的信息）
        self.known_packages = {
            "d865d6ed558b4214bdd26cea9116b508": "com.maplehaze.adsdk",  # 枫岚互联SDK
        }
    
    def get_correct_package_name(self, record):
        """获取正确的包名"""
        
        record_id = record.get('recordId', '')
        sdk_name = (record.get('sdkName') or '').lower()
        company_name = (record.get('companyName') or '').lower()
        official_web = (record.get('officialWeb') or '').lower()
        
        # 首先检查已知映射
        if record_id in self.known_packages:
            return self.known_packages[record_id], 'Known'
        
        # 精确的域名到包名映射
        if official_web:
            domain = urlparse(official_web).netloc.lower().replace('www.', '')
            
            domain_mappings = {
                'maplehaze.cn': 'com.maplehaze.adsdk',
                'sharetrace.com': 'com.sharetrace.sdk',
                'umeng.com': 'com.umeng.analytics',
                'mob.com': 'com.mob.sdk',
                'jpush.cn': 'cn.jpush.android',
                'getui.com': 'com.igexin.sdk',
                'sensorsdata.cn': 'com.sensorsdata.analytics.android.sdk',
                'growingio.com': 'com.growingio.android.sdk',
                'bugly.qq.com': 'com.tencent.bugly',
                'tingyun.com': 'com.tingyun.android',
                'baidu.com': 'com.baidu.mobads',
                'tencent.com': 'com.tencent.mm.opensdk',
                'qq.com': 'com.tencent.mm.opensdk',
                'huawei.com': 'com.huawei.hms',
                'xiaomi.com': 'com.xiaomi.mipush.sdk',
                'oppo.com': 'com.oppo.sdk',
                'vivo.com': 'com.vivo.sdk',
                'honor.com': 'com.honor.sdk',
                'heytap.com': 'com.heytap.sdk',
                'bytedance.com': 'com.bytedance.sdk',
                'douyin.com': 'com.ss.android.ugc.aweme',
                'volcengine.com': 'com.bytedance.sdk',
                'alibaba.com': 'com.alibaba.sdk',
                'alipay.com': 'com.alipay.sdk',
                'weibo.com': 'com.sina.weibo.sdk',
                'wechat.com': 'com.tencent.mm.opensdk',
                'qzone.qq.com': 'com.tencent.connect.sdk',
            }
            
            if domain in domain_mappings:
                return domain_mappings[domain], 'Domain'
            
            # 尝试从域名生成包名
            domain_parts = domain.split('.')
            if len(domain_parts) >= 2:
                if domain_parts[-1] == 'com':
                    return f"com.{domain_parts[-2]}.sdk", 'Generated'
                elif domain_parts[-1] == 'cn':
                    return f"cn.{domain_parts[-2]}.sdk", 'Generated'
        
        # 基于公司名的精确映射
        company_mappings = {
            '华为软件技术有限公司': 'com.huawei.hms',
            '深圳市腾讯计算机系统有限公司': 'com.tencent.mm.opensdk',
            '北京百度网讯科技有限公司': 'com.baidu.mobads',
            '北京火山引擎科技有限公司': 'com.bytedance.sdk',
            '荣耀终端股份有限公司': 'com.honor.sdk',
            '小米科技有限责任公司': 'com.xiaomi.mipush.sdk',
            '广东欢太科技有限公司': 'com.heytap.sdk',
            '北京微播视界科技有限公司': 'com.ss.android.ugc.aweme',
            '阿里巴巴（中国）有限公司': 'com.alibaba.sdk',
            '新浪微博': 'com.sina.weibo.sdk',
            '北京字节跳动科技有限公司': 'com.bytedance.sdk',
            '广州市久邦数码科技有限公司': 'com.gomo.sdk',
            '北京奇虎科技有限公司': 'com.qihoo.sdk',
            '网易（杭州）网络有限公司': 'com.netease.sdk',
            '上海哔哩哔哩科技有限公司': 'com.bilibili.sdk',
            '北京快手科技有限公司': 'com.kuaishou.sdk',
        }
        
        full_company = record.get('companyName', '')
        if full_company in company_mappings:
            return company_mappings[full_company], 'Company'
        
        # 基于SDK名称的特殊规则
        sdk_name_lower = sdk_name.lower()
        
        name_mappings = {
            'sharetrace': 'com.sharetrace.sdk',
            '个推': 'com.igexin.sdk',
            'getui': 'com.igexin.sdk',
            'jpush': 'cn.jpush.android',
            '极光推送': 'cn.jpush.android',
            'umeng': 'com.umeng.analytics',
            '友盟': 'com.umeng.analytics',
            'bugly': 'com.tencent.bugly',
            'tingyun': 'com.tingyun.android',
            '听云': 'com.tingyun.android',
            'mob': 'com.mob.sdk',
            'sensorsdata': 'com.sensorsdata.analytics.android.sdk',
            '神策': 'com.sensorsdata.analytics.android.sdk',
            'growingio': 'com.growingio.android.sdk',
            'talkingdata': 'com.talkingdata.sdk',
            'flurry': 'com.flurry.android',
            'admob': 'com.google.android.gms.ads',
            'facebook': 'com.facebook.android.sdk',
            'twitter': 'com.twitter.sdk.android',
            'linkedin': 'com.linkedin.android.sdk',
            'wechat': 'com.tencent.mm.opensdk',
            '微信': 'com.tencent.mm.opensdk',
            'qq': 'com.tencent.connect.sdk',
            'weibo': 'com.sina.weibo.sdk',
            '微博': 'com.sina.weibo.sdk',
            'alipay': 'com.alipay.sdk',
            '支付宝': 'com.alipay.sdk',
        }
        
        for keyword, package in name_mappings.items():
            if keyword in sdk_name_lower:
                return package, 'SDKName'
        
        # 基于类型的通用包名生成
        type_name = record.get('typeName', '')
        if type_name:
            # 清理SDK名称，生成合理的包名
            clean_name = sdk_name.replace(' ', '').replace('sdk', '').replace('安卓版', '').replace('android', '').lower()
            clean_name = ''.join(c for c in clean_name if c.isalnum())[:15]  # 只保留字母数字，限制长度
            
            if clean_name:
                return f"com.{clean_name}.sdk", 'Generated'
        
        # 如果都无法推断，返回None
        return None, 'Unknown'
    
    def fix_all_packages(self, input_file='enhanced_sdk_data.json', output_file='corrected_sdk_data.json'):
        """修正所有包名"""
        
        logging.info(f"开始修正 {input_file} 中的包名...")
        
        # 读取数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        sdk_list = data['data']
        total_count = len(sdk_list)
        
        logging.info(f"共有 {total_count} 条记录需要处理")
        
        stats = {
            'known': 0,
            'domain': 0,
            'company': 0,
            'sdk_name': 0,
            'generated': 0,
            'unknown': 0
        }
        
        for i, record in enumerate(sdk_list):
            if i % 100 == 0:
                logging.info(f"已处理 {i}/{total_count} 条记录")
            
            package_name, source = self.get_correct_package_name(record)
            
            if package_name:
                record['packageName'] = package_name
                record['packageSource'] = source
                
                if source == 'Known':
                    stats['known'] += 1
                elif source == 'Domain':
                    stats['domain'] += 1
                elif source == 'Company':
                    stats['company'] += 1
                elif source == 'SDKName':
                    stats['sdk_name'] += 1
                elif source == 'Generated':
                    stats['generated'] += 1
            else:
                # 移除错误的包名
                if 'packageName' in record:
                    del record['packageName']
                record['packageSource'] = 'Unknown'
                stats['unknown'] += 1
        
        # 保存修正后的数据
        data['package_fix_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
        data['package_fix_stats'] = stats
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logging.info(f"修正完成！数据已保存到 {output_file}")
        logging.info(f"统计结果:")
        logging.info(f"  - 已知准确包名: {stats['known']}")
        logging.info(f"  - 基于域名推断: {stats['domain']}")
        logging.info(f"  - 基于公司推断: {stats['company']}")
        logging.info(f"  - 基于SDK名推断: {stats['sdk_name']}")
        logging.info(f"  - 自动生成: {stats['generated']}")
        logging.info(f"  - 未知包名: {stats['unknown']}")
        
        # 显示一些示例
        logging.info("\n修正示例:")
        examples = []
        for record in sdk_list[:10]:
            if 'packageName' in record:
                examples.append(record)
            if len(examples) >= 5:
                break
        
        for i, record in enumerate(examples):
            package_name = record.get('packageName', 'Unknown')
            package_source = record.get('packageSource', 'Unknown')
            logging.info(f"{i+1}. {record['sdkName']}")
            logging.info(f"   包名: {package_name} ({package_source})")
        
        return stats

def main():
    """主函数"""
    fixer = QuickPackageFixer()
    
    try:
        stats = fixer.fix_all_packages()
        
        total_fixed = stats['known'] + stats['domain'] + stats['company'] + stats['sdk_name'] + stats['generated']
        total_records = sum(stats.values())
        
        logging.info("=" * 50)
        logging.info("✅ 包名修正完成！")
        logging.info(f"成功修正: {total_fixed}/{total_records} 条记录")
        logging.info("修正后的文件: corrected_sdk_data.json")
        logging.info("=" * 50)
        
    except Exception as e:
        logging.error(f"修正过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
