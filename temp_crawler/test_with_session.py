#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过session测试SDK API
"""

import requests
import json
import time

def test_with_session():
    """通过先访问主页建立session再测试API"""
    
    session = requests.Session()
    
    # 设置浏览器头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"'
    }
    
    session.headers.update(headers)
    
    print("步骤1: 访问主页获取session...")
    try:
        # 访问主页
        main_url = "https://sdk.caict.ac.cn/official/"
        response = session.get(main_url, timeout=10)
        print(f"主页访问状态码: {response.status_code}")
        print(f"获取到的cookies: {dict(session.cookies)}")
        
        # 等待一下
        time.sleep(1)
        
        # 访问具体页面
        page_url = "https://sdk.caict.ac.cn/official/#/public/sdklist/info/android"
        response = session.get(page_url, timeout=10)
        print(f"页面访问状态码: {response.status_code}")
        print(f"更新后的cookies: {dict(session.cookies)}")
        
    except Exception as e:
        print(f"访问主页失败: {str(e)}")
        return
    
    print("\n步骤2: 测试API端点...")
    
    # 更新headers为API请求
    api_headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    session.headers.update(api_headers)
    
    # 测试不同的API端点
    api_urls = [
        "https://sdk.caict.ac.cn/api/public/sdklist/info",
        "https://sdk.caict.ac.cn/official/api/public/sdklist/info",
        "https://sdk.caict.ac.cn/api/sdklist/info",
        "https://sdk.caict.ac.cn/official/api/sdklist/info"
    ]
    
    payload = {
        "platform": "1",
        "pageSize": 30,
        "page": 1,
        "typeId": "",
        "param": "",
        "isCompliance": 0,
        "isViolation": 0,
        "isNotOperate": 0,
        "isVersionUpdate": 0
    }
    
    for url in api_urls:
        print(f"\n测试API: {url}")
        print("-" * 50)
        
        try:
            response = session.post(url, json=payload, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
                    
                    if data.get('code') == 100:
                        print("✅ 成功找到可用的API端点!")
                        print(f"总记录数: {data.get('data', {}).get('total', 0)}")
                        print(f"当前页记录数: {len(data.get('data', {}).get('list', []))}")
                        return url
                        
                except json.JSONDecodeError:
                    print(f"响应内容: {response.text[:200]}...")
            else:
                print(f"响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")
    
    print("\n❌ 仍然没有找到可用的API端点")

if __name__ == "__main__":
    test_with_session()
