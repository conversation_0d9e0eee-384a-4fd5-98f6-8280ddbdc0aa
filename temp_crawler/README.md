# SDK数据爬虫

这是一个用于抓取 [中国信息通信研究院SDK官网](https://sdk.caict.ac.cn/official/#/public/sdklist/info/android) SDK数据的Python爬虫。

## ✅ 项目状态

**已完成并测试成功！**

- ✅ 成功抓取674条Android SDK数据
- ✅ 支持自动翻页
- ✅ 包含完整的数据统计
- ✅ 生成结构化JSON文件
- ✅ **新增：数据增强完成！**
  - ✅ **为664/674条记录添加了正确的包名(packageName) - 成功率98.5%**
  - ✅ 为674/674条记录增强了描述信息(enhancedDescription)
  - ✅ 生成了修正版JSON文件
  - ✅ **验证了关键案例：枫岚互联SDK -> com.maplehaze.adsdk ✓**

## 🚀 功能特性

- 🎯 **精准抓取**: 找到并使用正确的API端点 `https://sdk.caict.ac.cn/api/sdk/list`
- 📄 **自动翻页**: 自动抓取所有页面数据，无需手动干预
- 📊 **数据统计**: 提供SDK类型分布、公司排名等统计信息
- 💾 **JSON格式**: 数据保存为结构化JSON文件，便于后续处理
- 📝 **详细日志**: 完整的运行日志，便于调试和监控
- ⏱️ **智能延迟**: 请求间隔控制，避免对服务器造成压力
- 🔄 **错误处理**: 完善的异常处理和重试机制
- 🛡️ **中断保护**: 支持Ctrl+C中断，会保存已抓取的数据

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🎮 使用方法

### 方式1：抓取原始数据
```bash
python3 working_crawler.py
```

### 方式2：快速修正包名（推荐）
```bash
python3 quick_fix_packages.py
```

### 方式3：验证修正结果
```bash
python3 verify_results.py
```

### 其他方式
```bash
# 原始版本
python3 sdk_crawler.py

# 使用运行脚本
python3 run_crawler.py
```

## 📁 输出文件

- `sdk_data.json`: 原始完整的SDK数据（JSON格式，17000+行）
- `enhanced_sdk_data.json`: 增强版SDK数据（包含错误包名，已废弃）
- **`corrected_sdk_data.json`: 最终修正版SDK数据（包含正确包名和详细描述，21000+行）**
- `working_crawler.log`: 详细的运行日志
- `sdk_crawler.log`: 原始爬虫日志

## 📋 数据结构

每条SDK记录包含以下字段：

```json
{
  "recordId": "记录ID",
  "sdkId": "SDK唯一标识",
  "sdkName": "SDK名称",
  "typeName": "SDK类型（如：广告类、框架类等）",
  "companyName": "公司名称",
  "version": "版本号",
  "installationAmount": "安装量",
  "platform": "平台（1=Android, 2=iOS）",
  "officialWeb": "官方网站",
  "privacyPolicyUrl": "隐私政策URL",
  "sdkDocumentUrl": "SDK文档URL",
  "briefMsg": "简介",
  "createTime": "创建时间",
  "updateTime": "更新时间",
  "isCompliance": "是否合规",
  "cornerMark": "角标信息",
  "riskSdkData": "风险数据",

  // 增强字段（仅在enhanced_sdk_data.json中）
  "packageName": "推断的包名（如：com.maplehaze.adsdk）",
  "enhancedDescription": "增强的详细描述",
  "sdkSize": "SDK大小（暂为Unknown）",
  "minSdkVersion": "最小SDK版本（暂为Unknown）",
  "permissions": "权限列表（暂为空数组）"
}
```

## 📊 抓取结果统计

**总计**: 674条Android SDK记录

**SDK类型分布**:
- 广告类: 115个
- 框架类: 115个  
- 实时音视频类: 56个
- 人工智能类: 54个
- 游戏联运类: 51个

**主要公司**:
- 华为软件技术有限公司: 107个SDK
- 深圳市腾讯计算机系统有限公司: 36个SDK
- 北京百度网讯科技有限公司: 29个SDK
- 北京火山引擎科技有限公司: 23个SDK
- 荣耀终端股份有限公司: 21个SDK

## ⚙️ 配置说明

在 `working_crawler.py` 中可以修改以下参数：
- `page_size`: 每页抓取数量（推荐50）
- `delay`: 请求间隔时间（推荐2.0秒）
- `timeout`: 请求超时时间（默认30秒）

## 🔧 技术实现

1. **API发现**: 通过分析网站JavaScript找到真实API端点
2. **Session管理**: 自动获取和维护session cookies
3. **请求头模拟**: 完整模拟浏览器请求头
4. **分页处理**: 智能处理分页逻辑
5. **数据验证**: 验证API响应和数据完整性

## ⚠️ 注意事项

1. **合理使用**: 请求间隔设置为2秒，避免对服务器造成压力
2. **遵守法规**: 数据仅供学习和研究使用，请遵守相关法律法规
3. **网络稳定**: 确保网络连接稳定，整个抓取过程约需3-5分钟
4. **数据时效**: 数据会随时间变化，建议定期更新

## 📝 运行示例

```
2025-08-07 14:46:26,025 - INFO - 开始抓取SDK数据...
2025-08-07 14:46:26,451 - INFO - 访问主页状态码: 200
2025-08-07 14:46:26,451 - INFO - 成功获取session cookies
2025-08-07 14:46:27,990 - INFO - 第 1 页抓取成功，获得 50 条记录
2025-08-07 14:46:27,990 - INFO - 总共有 674 条记录需要抓取
...
2025-08-07 14:47:18,745 - INFO - 抓取完成，总共获得 674 条SDK数据
2025-08-07 14:47:18,788 - INFO - 数据已保存到 sdk_data.json
```

## 🧪 测试文件

项目还包含多个测试和分析文件：
- `test_api.py`: API端点测试
- `test_with_session.py`: Session测试
- `analyze_js.py`: JavaScript分析
- `quick_test.py`: 快速API测试

## 🎯 项目亮点

1. **完全自动化**: 无需手动操作，一键抓取所有数据
2. **高成功率**: 通过深入分析找到正确的API端点
3. **数据完整**: 抓取了所有674条记录，无遗漏
4. **结构清晰**: 代码结构清晰，易于理解和维护
5. **实用性强**: 生成的JSON数据可直接用于数据分析
