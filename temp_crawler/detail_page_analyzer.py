#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析详情页面，寻找获取详细信息的方法
"""

import requests
import json
import re
import time
from urllib.parse import urlparse, parse_qs

def analyze_detail_page():
    """分析详情页面的HTML和JavaScript"""
    
    session = requests.Session()
    
    # 设置浏览器头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
    }
    session.headers.update(headers)
    
    # 访问详情页面
    detail_url = "https://sdk.caict.ac.cn/official/#/public/sdk/detail?id=d865d6ed558b4214bdd26cea9116b508"
    
    print(f"访问详情页面: {detail_url}")
    
    try:
        # 先访问主页
        main_response = session.get("https://sdk.caict.ac.cn/official/", timeout=10)
        print(f"主页状态码: {main_response.status_code}")
        
        # 访问详情页面
        response = session.get(detail_url, timeout=10)
        print(f"详情页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            html_content = response.text
            
            # 保存页面内容
            with open('detail_page.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("详情页面HTML已保存到 detail_page.html")
            
            # 分析页面内容
            print("\n分析页面内容...")
            
            # 查找可能的API调用
            api_patterns = [
                r'["\']([^"\']*api[^"\']*detail[^"\']*)["\']',
                r'["\']([^"\']*detail[^"\']*api[^"\']*)["\']',
                r'detail["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'getDetail["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if len(match) > 5:
                        found_apis.add(match)
            
            if found_apis:
                print("找到可能的详情API:")
                for api in found_apis:
                    print(f"  - {api}")
            else:
                print("未在HTML中找到明显的详情API")
                
    except Exception as e:
        print(f"访问详情页面失败: {str(e)}")

def try_different_approach():
    """尝试不同的方法获取详情信息"""
    
    session = requests.Session()
    
    # 建立完整的session
    print("建立session...")
    
    # 1. 访问主页
    main_url = "https://sdk.caict.ac.cn/official/"
    response = session.get(main_url, timeout=10)
    print(f"主页访问: {response.status_code}")
    
    # 2. 访问列表页面
    list_url = "https://sdk.caict.ac.cn/official/#/public/sdklist/info/android"
    response = session.get(list_url, timeout=10)
    print(f"列表页访问: {response.status_code}")
    
    # 3. 访问详情页面
    detail_url = "https://sdk.caict.ac.cn/official/#/public/sdk/detail?id=d865d6ed558b4214bdd26cea9116b508"
    response = session.get(detail_url, timeout=10)
    print(f"详情页访问: {response.status_code}")
    
    print(f"当前cookies: {dict(session.cookies)}")
    
    # 4. 尝试API调用
    api_headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://sdk.caict.ac.cn',
        'Referer': 'https://sdk.caict.ac.cn/official/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    session.headers.update(api_headers)
    
    # 基于之前成功的列表API，尝试相似的详情API
    test_endpoints = [
        # 基于成功的 /api/sdk/list，尝试详情API
        "https://sdk.caict.ac.cn/api/sdk/detail",
        "https://sdk.caict.ac.cn/api/sdk/info",
        "https://sdk.caict.ac.cn/api/sdkinfo/detail",
        "https://sdk.caict.ac.cn/api/sdkinfo/info",
    ]
    
    record_id = "d865d6ed558b4214bdd26cea9116b508"
    
    for endpoint in test_endpoints:
        print(f"\n测试端点: {endpoint}")
        
        # 尝试不同的参数方式
        test_methods = [
            ('GET', {'id': record_id}),
            ('GET', {'recordId': record_id}),
            ('POST', {'id': record_id}),
            ('POST', {'recordId': record_id}),
        ]
        
        for method, params in test_methods:
            print(f"  {method} {params}")
            
            try:
                if method == 'GET':
                    response = session.get(endpoint, params=params, timeout=10)
                else:
                    response = session.post(endpoint, json=params, timeout=10)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 100:
                            print(f"    ✅ 成功! 数据: {json.dumps(data, ensure_ascii=False)[:200]}...")
                            return endpoint, method, params
                        else:
                            print(f"    响应: {data}")
                    except:
                        print(f"    非JSON: {response.text[:100]}...")
                        
            except Exception as e:
                print(f"    错误: {str(e)}")
    
    return None

def check_existing_data_for_details():
    """检查现有数据中是否已经包含了一些详细信息"""
    
    try:
        with open('sdk_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("检查现有数据结构...")
        
        if data['data']:
            first_record = data['data'][0]
            print("第一条记录的字段:")
            for key, value in first_record.items():
                print(f"  {key}: {type(value).__name__} - {str(value)[:100]}...")
            
            # 检查是否已经有包名或详细描述
            has_package = any('package' in key.lower() for key in first_record.keys())
            has_description = any(key in ['description', 'detailMsg', 'fullDescription'] for key in first_record.keys())
            
            print(f"\n是否包含包名信息: {has_package}")
            print(f"是否包含详细描述: {has_description}")
            
            # 检查briefMsg字段
            brief_msgs = [record.get('briefMsg', '') for record in data['data'][:10]]
            non_empty_briefs = [msg for msg in brief_msgs if msg and msg.strip()]
            
            print(f"前10条记录中有 {len(non_empty_briefs)} 条有briefMsg内容")
            
            if non_empty_briefs:
                print("示例briefMsg:")
                for i, msg in enumerate(non_empty_briefs[:3]):
                    print(f"  {i+1}: {msg}")
                    
    except Exception as e:
        print(f"检查现有数据失败: {str(e)}")

if __name__ == "__main__":
    print("=== 分析详情页面 ===")
    analyze_detail_page()
    
    print("\n=== 尝试不同方法 ===")
    result = try_different_approach()
    
    if result:
        print(f"\n✅ 找到可用方法: {result}")
    else:
        print("\n❌ 未找到可用方法")
    
    print("\n=== 检查现有数据 ===")
    check_existing_data_for_details()
