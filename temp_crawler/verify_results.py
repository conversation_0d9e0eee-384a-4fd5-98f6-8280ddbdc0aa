#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修正结果
"""

import json
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def verify_corrected_data():
    """验证修正后的数据"""
    
    logging.info("验证修正后的数据...")
    
    with open('corrected_sdk_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    sdk_list = data['data']
    total_count = len(sdk_list)
    
    # 统计信息
    package_stats = {}
    source_stats = {}
    
    # 特殊验证案例
    test_cases = {
        "d865d6ed558b4214bdd26cea9116b508": {
            "expected_package": "com.maplehaze.adsdk",
            "sdk_name": "枫岚互联SDK"
        }
    }
    
    logging.info(f"总记录数: {total_count}")
    
    # 验证特殊案例
    logging.info("\n验证特殊案例:")
    for record_id, expected in test_cases.items():
        found = False
        for record in sdk_list:
            if record.get('recordId') == record_id:
                found = True
                actual_package = record.get('packageName')
                expected_package = expected['expected_package']
                sdk_name = expected['sdk_name']
                
                if actual_package == expected_package:
                    logging.info(f"✅ {sdk_name}: {actual_package} (正确)")
                else:
                    logging.info(f"❌ {sdk_name}: 期望 {expected_package}, 实际 {actual_package}")
                break
        
        if not found:
            logging.info(f"❌ 未找到记录ID: {record_id}")
    
    # 统计包名来源
    for record in sdk_list:
        package_name = record.get('packageName')
        package_source = record.get('packageSource', 'Unknown')
        
        if package_name:
            package_stats['有包名'] = package_stats.get('有包名', 0) + 1
        else:
            package_stats['无包名'] = package_stats.get('无包名', 0) + 1
        
        source_stats[package_source] = source_stats.get(package_source, 0) + 1
    
    logging.info(f"\n包名统计:")
    for key, value in package_stats.items():
        logging.info(f"  {key}: {value}")
    
    logging.info(f"\n包名来源统计:")
    for key, value in source_stats.items():
        logging.info(f"  {key}: {value}")
    
    # 显示各种来源的示例
    logging.info(f"\n各来源示例:")
    
    examples_by_source = {}
    for record in sdk_list:
        source = record.get('packageSource', 'Unknown')
        if source not in examples_by_source:
            examples_by_source[source] = []
        if len(examples_by_source[source]) < 3:
            examples_by_source[source].append(record)
    
    for source, examples in examples_by_source.items():
        logging.info(f"\n{source} 来源示例:")
        for i, record in enumerate(examples):
            package_name = record.get('packageName', 'N/A')
            sdk_name = record.get('sdkName', 'N/A')
            logging.info(f"  {i+1}. {sdk_name} -> {package_name}")
    
    # 检查可能的问题
    logging.info(f"\n质量检查:")
    
    # 检查重复包名
    package_counts = {}
    for record in sdk_list:
        package_name = record.get('packageName')
        if package_name:
            package_counts[package_name] = package_counts.get(package_name, 0) + 1
    
    duplicates = {k: v for k, v in package_counts.items() if v > 1}
    if duplicates:
        logging.info(f"发现重复包名:")
        for package, count in list(duplicates.items())[:5]:
            logging.info(f"  {package}: {count} 次")
    else:
        logging.info("✅ 没有发现重复包名")
    
    # 检查包名格式
    invalid_packages = []
    for record in sdk_list:
        package_name = record.get('packageName')
        if package_name:
            # 简单的包名格式检查
            if not (package_name.startswith('com.') or package_name.startswith('cn.') or package_name.startswith('org.')):
                invalid_packages.append((record.get('sdkName'), package_name))
    
    if invalid_packages:
        logging.info(f"发现可能的无效包名格式:")
        for sdk_name, package in invalid_packages[:5]:
            logging.info(f"  {sdk_name}: {package}")
    else:
        logging.info("✅ 包名格式检查通过")
    
    # 最终统计
    success_rate = (package_stats.get('有包名', 0) / total_count) * 100
    logging.info(f"\n最终统计:")
    logging.info(f"  成功率: {success_rate:.1f}% ({package_stats.get('有包名', 0)}/{total_count})")
    
    return {
        'total': total_count,
        'with_package': package_stats.get('有包名', 0),
        'without_package': package_stats.get('无包名', 0),
        'success_rate': success_rate,
        'source_stats': source_stats
    }

def main():
    """主函数"""
    try:
        results = verify_corrected_data()
        
        logging.info("=" * 50)
        logging.info("✅ 验证完成！")
        logging.info(f"成功为 {results['with_package']}/{results['total']} 条记录添加了包名")
        logging.info(f"成功率: {results['success_rate']:.1f}%")
        logging.info("=" * 50)
        
    except Exception as e:
        logging.error(f"验证过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
