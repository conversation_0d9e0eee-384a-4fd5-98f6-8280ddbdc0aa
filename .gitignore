# 忽略 tenyy 目录下的 storage 文件夹
tenyy/storage/
storage/
tenyy.egg-info/
signoz/
grafana-data/
postgres-data/
redis-data/
victorialogs-data/
promtail-data/
logs/
logs/app.json
aria2_config/
aria2_downloads/
PRPs/
tenyy/container/data

# 忽略所有 .log 文件
*.log

# 忽略所有 APK 文件
*.apk

# 忽略所有 __pycache__ 文件夹
__pycache__/
**/__pycache__/

# 环略环境变量文件
.env
tenyy/.env
*/.env

# Docker构建缓存文件 (镜像体积优化)
.last-requirements-hash
.last-dockerfile-hash

# YoYo AI version control directory
.yoyo/
